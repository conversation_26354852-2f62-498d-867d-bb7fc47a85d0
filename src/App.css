@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@400;500;600;700&display=swap');

/* Base styles */
:root {
  --primary-color: #9e9e9e; /* Soft silver */
  --secondary-color: #7e8fa3; /* Muted blue-gray */
  --accent-color: #6d8eff; /* Softer blue accent */
  --background-color: #0f0f0f; /* Deeper dark background */
  --dark-surface: #1a1a1a; /* Slightly lighter than background */
  --surface-color: #202020; /* Card background */
  --text-color: #e6e6e6; /* Soft white text */
  --light-text: #999; /* Muted gray text */
  --white: #f7f7f7; /* Clean off-white */
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --border-color: rgba(255, 255, 255, 0.08);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --download-gradient: linear-gradient(135deg, #00B4DB, #0083B0);
  --download-shadow: 0 8px 24px rgba(0, 180, 219, 0.4);
  --download-hover-gradient: linear-gradient(135deg, #00B4DB, #3a7bd5);
}

/* Enable smooth scrolling for the whole page */
html {
  scroll-behavior: smooth;
}

/* More elegant fade animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-title {
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

.description {
  max-width: 700px;
  margin-bottom: 3rem;
  background-color: var(--surface-color);
  padding: 2.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) 0.1s forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.description p {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
}

.emphasis {
  font-style: normal;
  color: var(--accent-color);
  font-weight: 500;
}

.cta-buttons {
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) 0.2s forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.features h2, .section-title, .page-title {
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.feature-card, .about-card, .team-card {
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  animation-fill-mode: both;
  animation-delay: calc(0.05s * var(--index, 0));
}

/* Element animations with staggered delays */
.feature-card:nth-child(1), .about-card:nth-child(1) { --index: 1; }
.feature-card:nth-child(2), .about-card:nth-child(2) { --index: 2; }
.feature-card:nth-child(3), .about-card:nth-child(3) { --index: 3; }
.feature-card:nth-child(4) { --index: 4; }

.team-grid .team-card:nth-child(1) { --index: 1; }
.team-grid .team-card:nth-child(2) { --index: 2; }
.team-grid .team-card:nth-child(3) { --index: 3; }
.team-grid .team-card:nth-child(4) { --index: 4; }
.team-grid .team-card:nth-child(5) { --index: 5; }
.team-grid .team-card:nth-child(6) { --index: 6; }
.team-grid .team-card:nth-child(7) { --index: 7; }
.team-grid .team-card:nth-child(8) { --index: 8; }

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 600;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Main content styles */
.main {
  flex: 1;
  padding: 4rem 0;
}

.docs-main-content {
  flex: 1;
  padding: 0;
  min-height: 100vh;
}

/* Hero section */
.hero {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 5rem;
  padding-top: 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 3px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.description {
  max-width: 700px;
  margin-bottom: 3rem;
  background-color: var(--surface-color);
  padding: 2.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) 0.1s forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.description p {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
}

.emphasis {
  font-style: normal;
  color: var(--accent-color);
  font-weight: 500;
}

.cta-buttons {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.9rem 1.8rem;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
  letter-spacing: 0.5px;
  border: none;
  font-size: 1rem;
  cursor: pointer;
}

.button svg {
  width: 18px;
  height: 18px;
}

.button.primary {
  background-color: transparent;
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 1rem 2rem;
}

.button.primary svg {
  width: 20px;
  height: 20px;
  opacity: 0.8;
}

.button.secondary {
  background-color: transparent;
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 1rem 2rem;
}

.button.secondary svg {
  width: 20px;
  height: 20px;
  opacity: 0.8;
}

/* Features section */
.features {
  margin-bottom: 6rem;
}

.features h2, .section-title {
  text-align: center;
  font-size: 2.2rem;
  color: var(--white);
  margin-bottom: 3.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.features h2::after, .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
  border-radius: 2px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
}

.feature-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  opacity: 0;
  transition: var(--transition);
}



.feature-icon {
  width: 70px;
  height: 70px;
  background-color: var(--dark-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 1.8rem;
  color: var(--accent-color);
  transition: var(--transition);
}



.feature-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--white);
  transition: var(--transition);
}



.feature-card p {
  color: var(--light-text);
  line-height: 1.7;
}

/* Floating iOS-style Navbar */
.navbar {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: auto;
}

.navbar-floating {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 0.8rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.navbar-content {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 0.5rem 0.8rem;
  border-radius: 12px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: 0.75rem;
  font-weight: 500;
  min-width: 60px;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-item.active {
  color: var(--accent-color);
  background: rgba(109, 142, 255, 0.15);
}

.nav-item svg {
  transition: all 0.3s ease;
}

.nav-item:hover svg {
  transform: scale(1.1);
}

.nav-item span {
  font-size: 0.7rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.moon-logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(109, 142, 255, 0.3);
}

.moon-logo::after {
  content: '';
  position: absolute;
  width: 75%;
  height: 75%;
  background-color: var(--dark-surface);
  border-radius: 50%;
  top: -15%;
  right: -15%;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
}



.nav-link {
  color: var(--light-text);
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
  font-size: 1rem;
  position: relative;
  padding: 8px 0;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-color);
  transition: var(--transition);
  border-radius: 1px;
}

.nav-link:hover,
.nav-link.active {
  color: var(--white);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Language Toggle Button */
.language-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.language-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(109, 142, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.language-toggle:hover::before {
  left: 100%;
}



.language-toggle:active {
  transform: translateY(0) scale(0.98);
}

.language-toggle svg {
  width: 16px;
  height: 16px;
  transition: var(--transition);
}



.language-toggle.transitioning {
  opacity: 0.7;
  pointer-events: none;
  background-color: rgba(109, 142, 255, 0.05);
}

.language-toggle.transitioning svg {
  animation: languageSpinner 0.6s ease-in-out;
}

@keyframes languageSpinner {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Language Toggle Container */
.language-toggle-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Auto-detected language indicator */
.language-toggle.auto-detected {
  position: relative;
  border-color: rgba(34, 197, 94, 0.3);
}



.auto-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #22c55e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: autoDetectedPulse 2s infinite;
}

.auto-indicator svg {
  width: 4px;
  height: 4px;
  color: white;
}

@keyframes autoDetectedPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Reset to auto button */
.reset-auto-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-color-secondary);
  cursor: pointer;
  transition: var(--transition);
  opacity: 0.7;
}



.reset-auto-button:active {
  transform: rotate(180deg) scale(0.95);
}



/* Language Content Animation */
.language-content {
  animation: languageFadeIn 0.4s ease-out;
}

@keyframes languageFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page transition animations for language changes */
.page-content {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-content.language-changing {
  opacity: 0.7;
  transform: translateY(5px);
}

/* Footer styles */
.footer {
  background-color: var(--dark-surface);
  padding: 3rem 0;
  border-top: 1px solid var(--border-color);
  margin-top: 4rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.footer-logo .moon-logo {
  width: 28px;
  height: 28px;
}

.footer-logo span {
  font-weight: 600;
  color: var(--white);
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.2rem;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  color: var(--light-text);
  text-decoration: none;
  transition: var(--transition);
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
}



.copyright {
  text-align: center;
  color: var(--light-text);
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 1rem;
}

/* About page styles */
.about-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 5rem;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 3.5rem;
  text-transform: uppercase;
  letter-spacing: 3px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  width: 100%;
}

.about-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  opacity: 0;
  transition: var(--transition);
}

.about-icon {
  width: 70px;
  height: 70px;
  background-color: var(--dark-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto 1.8rem;
  color: var(--accent-color);
  transition: var(--transition);
}



.about-card h2 {
  font-size: 1.6rem;
  margin-bottom: 1.2rem;
  color: var(--white);
}

.about-card p {
  color: var(--light-text);
  line-height: 1.8;
}

.about-cta {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* Team Section Styles */
.team-section {
  margin-bottom: 6rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2.5rem;
}

.team-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  opacity: 0;
  transition: var(--transition);
}



.team-avatar {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: 3px solid transparent;
  background: linear-gradient(var(--surface-color), var(--surface-color)) padding-box,
              linear-gradient(135deg, var(--accent-color), var(--primary-color)) border-box;
  transition: var(--transition);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}



.team-card h3 {
  font-size: 1.3rem;
  color: var(--white);
  margin-bottom: 0.5rem;
}

/* FAQ Styles */
.faqs-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 850px;
  margin: 0 auto;
}

.faq-item {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 1.8rem 2rem;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  animation-fill-mode: both;
  cursor: pointer;
}

.faq-item:nth-child(1) { animation-delay: 0.1s; }
.faq-item:nth-child(2) { animation-delay: 0.15s; }
.faq-item:nth-child(3) { animation-delay: 0.2s; }
.faq-item:nth-child(4) { animation-delay: 0.25s; }
.faq-item:nth-child(5) { animation-delay: 0.3s; }
.faq-item:nth-child(6) { animation-delay: 0.35s; }
.faq-item:nth-child(7) { animation-delay: 0.4s; }
.faq-item:nth-child(8) { animation-delay: 0.45s; }
.faq-item:nth-child(9) { animation-delay: 0.5s; }
.faq-item:nth-child(10) { animation-delay: 0.55s; }
.faq-item:nth-child(11) { animation-delay: 0.6s; }
.faq-item:nth-child(12) { animation-delay: 0.65s; }



.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-arrow {
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

.faq-item.active .faq-arrow {
  transform: rotate(180deg);
  color: var(--accent-color);
}

.faq-item h3 {
  color: var(--white);
  margin-bottom: 0;
  font-size: 1.2rem;
  letter-spacing: 0.5px;
  flex: 1;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease, margin 0.3s ease;
}

.faq-item.active .faq-answer {
  max-height: 500px;
  opacity: 1;
  margin-top: 1.5rem;
}

.faq-item p {
  color: var(--light-text);
  line-height: 1.7;
}

/* Responsive styles */
@media (max-width: 768px) {
  .navbar {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    transform: none;
    width: auto;
  }

  .navbar-floating {
    padding: 0.6rem 1rem;
  }

  .navbar-content {
    gap: 1rem;
  }

  .nav-item {
    min-width: 50px;
    padding: 0.4rem 0.6rem;
  }

  .nav-item span {
    font-size: 0.65rem;
  }

  .footer-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .footer {
    padding: 2rem 0;
  }

  .footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .social-link {
    justify-content: center;
    padding: 12px 16px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 48px;
    display: flex;
    align-items: center;
  }



  .footer-logo {
    justify-content: center;
    margin-bottom: 0.5rem;
    animation: fadeInUp 0.6s ease-out;
  }

  .footer-logo span {
    font-size: 1.4rem;
  }

  .footer-links {
    animation: fadeInUp 0.8s ease-out;
  }

  .social-link:nth-child(1) { animation-delay: 0.1s; }
  .social-link:nth-child(2) { animation-delay: 0.2s; }
  .social-link:nth-child(3) { animation-delay: 0.3s; }
  .social-link:nth-child(4) { animation-delay: 0.4s; }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .hero {
    margin-bottom: 3rem;
    padding-top: 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    letter-spacing: 2px;
  }

  .description {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .description p {
    font-size: 1rem;
    line-height: 1.6;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
    margin-bottom: 1.2rem;
  }

  .description {
    padding: 1.25rem;
    margin-bottom: 1.2rem;
  }

  .cta-buttons {
    gap: 0.8rem;
  }

  .button.primary, .button.secondary {
    padding: 0.8rem 1.6rem;
    font-size: 0.95rem;
  }

  .button.primary svg, .button.secondary svg {
    width: 18px;
    height: 18px;
  }

  /* Enhanced footer for very small devices */
  .footer {
    padding: 1.5rem 0;
  }

  .footer-content {
    gap: 1rem;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    max-width: 280px;
  }

  .social-link {
    padding: 10px 14px;
    font-size: 0.9rem;
  }

  .social-link svg {
    width: 18px;
    height: 18px;
  }

  .social-link span {
    font-size: 0.9rem;
  }

  .footer-logo span {
    font-size: 1.2rem;
  }

  .copyright {
    font-size: 0.8rem;
    padding: 0 1rem;
    line-height: 1.4;
  }
}

/* Privacy Policy Styles */
.privacy-policy {
  padding: 3rem 0;
}

.privacy-policy-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 5rem;
  max-width: 850px;
  margin-left: auto;
  margin-right: auto;
}

.privacy-policy-section h1 {
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  width: 100%;
}

.privacy-policy-section h2 {
  font-size: 2rem;
  color: var(--white);
  margin-top: 2.5rem;
  margin-bottom: 1.2rem;
}

.privacy-policy-section h3 {
  font-size: 1.7rem;
  color: var(--white);
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.privacy-policy-section h4 {
  font-size: 1.4rem;
  color: var(--white);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.privacy-policy-section p {
  color: var(--light-text);
  line-height: 1.8;
  margin-bottom: 1.2rem;
}

.privacy-policy-section ul {
  margin: 1.2rem 0 1.5rem;
  padding-left: 1.8rem;
  color: var(--light-text);
}

.privacy-policy-section ul li {
  margin-bottom: 1rem;
  line-height: 1.8;
}

.privacy-policy-section a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.2s;
}

.privacy-policy-section a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Media queries for privacy policy responsiveness */
@media (max-width: 768px) {
  .privacy-policy-section h1 {
    font-size: 2.2rem;
  }
  
  .privacy-policy-section h2 {
    font-size: 1.8rem;
  }
  
  .privacy-policy-section h3 {
    font-size: 1.5rem;
  }
  
  .privacy-policy-section h4 {
    font-size: 1.3rem;
  }
}

/* Animation classes */
.animate-fade {
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.privacy-policy-content {
  animation-delay: 0.2s;
}

.privacy-policy-content > * {
  animation: fadeInUp 0.6s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  animation-fill-mode: both;
}

.privacy-policy-content > *:nth-child(1) { animation-delay: 0.3s; }
.privacy-policy-content > *:nth-child(2) { animation-delay: 0.4s; }
.privacy-policy-content > *:nth-child(3) { animation-delay: 0.5s; }
.privacy-policy-content > *:nth-child(4) { animation-delay: 0.55s; }
.privacy-policy-content > *:nth-child(5) { animation-delay: 0.6s; }
.privacy-policy-content > *:nth-child(6) { animation-delay: 0.65s; }
.privacy-policy-content > *:nth-child(7) { animation-delay: 0.7s; }
.privacy-policy-content > *:nth-child(8) { animation-delay: 0.75s; }
.privacy-policy-content > *:nth-child(9) { animation-delay: 0.8s; }
.privacy-policy-content > *:nth-child(10) { animation-delay: 0.85s; }

/* Not Found Page Styles */
.not-found {
  padding: 6rem 0;
}

.not-found-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.not-found-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 550px;
  animation: fadeInUp 1s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

.not-found-icon {
  width: 150px;
  height: 150px;
  background-color: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 2.5rem;
  color: var(--accent-color);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

.not-found-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.not-found-text {
  color: var(--light-text);
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
}

.not-found-actions {
  display: flex;
  gap: 1.2rem;
}

@media (max-width: 768px) {
  .not-found-title {
    font-size: 2.5rem;
  }
  
  .not-found-icon {
    width: 120px;
    height: 120px;
  }
}

/* Custom Cursor Styles - Desktop Only */
@media (min-width: 1024px) and (pointer: fine) {
  /* Hide default cursor */
  * {
    cursor: none !important;
  }
  
  /* Hover effects for interactive elements */
  a, button, .button, .nav-link, .social-link {
    cursor: none !important;
  }
  
  a:hover .custom-cursor-container svg,
  button:hover .custom-cursor-container svg,
  .button:hover .custom-cursor-container svg,
  .nav-link:hover .custom-cursor-container svg,
  .social-link:hover .custom-cursor-container svg {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* Repository Stats */
.repo-stats {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 2.5rem;
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) 0.1s forwards;
  opacity: 0;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  transition: var(--transition);
}



.stat-item svg {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .repo-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1.5rem;
  }
  
  .stat-item {
    justify-content: center;
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .stat-item svg {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .repo-stats {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
    gap: 0.6rem;
  }
  
  .stat-item {
    padding: 0.35rem 0.7rem;
    font-size: 0.85rem;
  }
}

/* Repository Cards */
.repo-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.repo-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  animation: fadeInUp 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  animation-fill-mode: both;
  animation-delay: calc(0.1s * var(--index, 0));
  position: relative;
  overflow: hidden;
}

.repo-card:nth-child(1) { --index: 1; }
.repo-card:nth-child(2) { --index: 2; }
.repo-card:nth-child(3) { --index: 3; }

.repo-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  opacity: 0;
  transition: var(--transition);
}



.repo-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.repo-card-header svg {
  color: var(--accent-color);
  min-width: 24px;
  min-height: 24px;
}

.repo-card-header h3 {
  color: var(--white);
  font-size: 1.4rem;
  margin: 0;
}

.repo-card p {
  color: var(--light-text);
  line-height: 1.7;
}

@media (max-width: 768px) {
  .repo-stats {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

/* Image Viewer Styles */
.image-viewer {
  width: 100%;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}













/* Image Carousel Section */
.image-carousel {
  padding: 2rem 0;
  margin: 2rem 0 4rem;
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 280px;
  overflow: hidden;
}

.carousel-track {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-slide {
  position: absolute;
  width: 45%;
  height: 100%;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.carousel-slide.active {
  width: 55%;
  z-index: 3;
  transform: translateX(0) scale(1);
  filter: blur(0px) brightness(1);
}

.carousel-slide.prev {
  width: 35%;
  z-index: 1;
  transform: translateX(-85%) scale(0.75);
  filter: blur(2px) brightness(0.6);
  opacity: 0.7;
}

.carousel-slide.next {
  width: 35%;
  z-index: 1;
  transform: translateX(85%) scale(0.75);
  filter: blur(2px) brightness(0.6);
  opacity: 0.7;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  z-index: 5;
  backdrop-filter: blur(10px);
}

.carousel-nav:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: translateY(-50%) scale(1);
}

.carousel-nav-prev {
  left: 2rem;
}

.carousel-nav-next {
  right: 2rem;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  cursor: pointer;
  transition: var(--transition);
}

.carousel-indicator.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  box-shadow: 0 0 10px rgba(109, 142, 255, 0.5);
}

.carousel-indicator:hover {
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

@media (max-width: 768px) {
  .image-carousel {
    padding: 1.5rem 0;
    margin: 1.5rem 0 3rem;
  }

  .carousel-container {
    height: 220px;
  }

  .carousel-slide {
    width: 60%;
  }

  .carousel-slide.active {
    width: 70%;
  }

  .carousel-slide.prev,
  .carousel-slide.next {
    width: 45%;
    transform: translateX(-90%) scale(0.65);
    filter: blur(3px) brightness(0.5);
    opacity: 0.6;
  }

  .carousel-slide.next {
    transform: translateX(90%) scale(0.65);
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
  }

  .carousel-nav-prev {
    left: 1rem;
  }

  .carousel-nav-next {
    right: 1rem;
  }

  .carousel-indicators {
    margin-top: 1.5rem;
  }

  .carousel-indicator {
    width: 10px;
    height: 10px;
  }
}

/* Mobile optimizations for features and repository sections */
@media (max-width: 768px) {
  .features {
    margin-bottom: 4rem;
  }
  
  .features h2, .section-title {
    font-size: 1.8rem;
    margin-bottom: 2.5rem;
    letter-spacing: 1.5px;
  }
  
  .features h2::after, .section-title::after {
    width: 40px;
    height: 2.5px;
    bottom: -8px;
  }
  
  .feature-grid, .repo-cards {
    gap: 1.5rem;
  }
  
  .feature-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .repo-cards {
    grid-template-columns: 1fr;
    margin-top: 2rem;
  }
  
  .feature-card, .repo-card {
    padding: 1.8rem;
  }
  
  .feature-card h3, .repo-card-header h3 {
    font-size: 1.3rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 1.5rem;
  }
  
  .feature-card p, .repo-card p {
    font-size: 0.95rem;
    line-height: 1.6;
  }
}

@media (max-width: 480px) {
  .features {
    margin-bottom: 3rem;
  }
  
  .features h2, .section-title {
    font-size: 1.6rem;
    margin-bottom: 2rem;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
  
  .feature-card, .repo-card {
    padding: 1.5rem;
  }
  
  .feature-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 1.2rem;
  }
  
  .feature-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .repo-card-header svg {
    min-width: 20px;
    min-height: 20px;
  }
} 