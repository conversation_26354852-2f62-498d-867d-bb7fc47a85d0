import React from 'react';
import { HashRouter, Routes, Route, Link, Navigate, useLocation } from 'react-router-dom';
import './App.css';
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import DiscordRedirect from './pages/DiscordRedirect';
import FAQsPage from './pages/FAQsPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import NotFoundPage from './pages/NotFoundPage';
// Documentation pages
import DocsOverviewPage from './pages/docs/DocsOverviewPage';
import DocsCodebaseOverviewPage from './pages/docs/DocsCodebaseOverviewPage';
import DocsModuleDocumentationPage from './pages/docs/DocsModuleDocumentationPage';
import DocsApplicationUsagePage from './pages/docs/DocsApplicationUsagePage';
import DocsIntegrationGuidesPage from './pages/docs/DocsIntegrationGuidesPage';
import DocsTechnicalTopicsPage from './pages/docs/DocsTechnicalTopicsPage';
import DocsModuleDevelopmentPage from './pages/docs/DocsModuleDevelopmentPage';
import DocsRemoteLinkSystemPage from './pages/docs/DocsRemoteLinkSystemPage';
import DocsGameModulesPage from './pages/docs/DocsGameModulesPage';
import DocsCustomServersPage from './pages/docs/DocsCustomServersPage';
import { LanguageProvider } from './contexts/LanguageContext';
import Navbar from './components/Navbar';
import ScrollToTop from './components/ScrollToTop';
import CustomCursor from './components/CustomCursor';
import { initializeIPLogger } from './utils/ipLogger';

const NonDocsContent: React.FC = () => {
  return (
    <LanguageProvider>
      <div className="app">
        <ScrollToTop />
        <CustomCursor />
        <Navbar />
        <main className="main">
          <Routes>
            <Route path="/about" element={<AboutPage />} />
            <Route path="/faqs" element={<FAQsPage />} />
            <Route path="/discord" element={<DiscordRedirect />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/" element={<HomePage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </main>
        <footer className="footer">
          <div className="container">
            <div className="footer-content">
              <div className="footer-logo">
                <div className="moon-logo"></div>
                <span>Project Lumina</span>
              </div>
              <div className="footer-links">
                <a href="https://github.com/TheProjectLumina/LuminaClient" className="social-link">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.477 2 2 6.477 2 12C2 16.418 4.865 20.166 8.839 21.489C9.339 21.581 9.5 21.277 9.5 21.012C9.5 20.775 9.492 20.063 9.489 19.192C6.726 19.826 6.139 17.959 6.139 17.959C5.685 16.811 5.028 16.508 5.028 16.508C4.128 15.883 5.095 15.895 5.095 15.895C6.092 15.965 6.624 16.928 6.624 16.928C7.521 18.457 8.97 18.007 9.52 17.752C9.611 17.099 9.87 16.649 10.153 16.419C7.93 16.187 5.596 15.332 5.596 11.551C5.596 10.399 6.007 9.456 6.644 8.718C6.542 8.465 6.178 7.501 6.744 6.131C6.744 6.131 7.586 5.863 9.478 7.151C10.295 6.929 11.15 6.819 12.002 6.814C12.852 6.819 13.707 6.929 14.526 7.151C16.416 5.863 17.256 6.131 17.256 6.131C17.824 7.501 17.459 8.465 17.357 8.718C17.996 9.456 18.403 10.399 18.403 11.551C18.403 15.343 16.065 16.184 13.834 16.412C14.189 16.694 14.5 17.254 14.5 18.102C14.5 19.31 14.488 20.686 14.488 21.012C14.488 21.279 14.648 21.586 15.155 21.486C19.137 20.161 22 16.415 22 12C22 6.477 17.523 2 12 2Z" fill="currentColor"/>
                  </svg>
                  <span>GitHub</span>
                </a>
                <Link to="/discord" className="social-link">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-discord" viewBox="0 0 16 16">
                    <path d="M13.545 2.907a13.2 13.2 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.2 12.2 0 0 0-3.658 0 8 8 0 0 0-.412-.833.05.05 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.04.04 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032q.003.022.021.037a13.3 13.3 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019q.463-.63.818-1.329a.05.05 0 0 0-.01-.059l-.018-.011a9 9 0 0 1-1.248-.595.05.05 0 0 1-.02-.066l.015-.019q.127-.095.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.05.05 0 0 1 .053.007q.121.1.248.195a.05.05 0 0 1-.004.085 8 8 0 0 1-1.249.594.05.05 0 0 0-.03.03.05.05 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.2 13.2 0 0 0 4.001-2.02.05.05 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.03.03 0 0 0-.02-.019m-8.198 7.307c-.789 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612m5.316 0c-.788 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612"/>
                  </svg>
                  <span>Discord</span>
                </Link>
                <Link to="/docs/overview" className="social-link">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M14 2V8H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M16 13H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M16 17H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M10 9H9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Documentation</span>
                </Link>
                <Link to="/privacy-policy" className="social-link">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 16V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Privacy Policy</span>
                </Link>
              </div>
            </div>
            <div className="copyright">
              &copy; {new Date().getFullYear()} Project Lumina. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </LanguageProvider>
  );
};

const DocsContent: React.FC = () => {
  return (
    <div className="app">
      <ScrollToTop />
      <CustomCursor />
      <main className="main docs-main-content">
        <Routes>
          <Route path="/docs" element={<Navigate to="/docs/overview" replace />} />
          <Route path="/docs/overview" element={<LanguageProvider><DocsOverviewPage /></LanguageProvider>} />
          <Route path="/docs/codebase-overview" element={<LanguageProvider><DocsCodebaseOverviewPage /></LanguageProvider>} />
          <Route path="/docs/module-documentation" element={<LanguageProvider><DocsModuleDocumentationPage /></LanguageProvider>} />
          <Route path="/docs/module-development" element={<LanguageProvider><DocsModuleDevelopmentPage /></LanguageProvider>} />
          <Route path="/docs/remotelink-system" element={<LanguageProvider><DocsRemoteLinkSystemPage /></LanguageProvider>} />
          <Route path="/docs/game-modules" element={<LanguageProvider><DocsGameModulesPage /></LanguageProvider>} />
          <Route path="/docs/custom-servers" element={<LanguageProvider><DocsCustomServersPage /></LanguageProvider>} />
          <Route path="/docs/application-usage" element={<LanguageProvider><DocsApplicationUsagePage /></LanguageProvider>} />
          <Route path="/docs/integration-guides" element={<LanguageProvider><DocsIntegrationGuidesPage /></LanguageProvider>} />
          <Route path="/docs/technical-topics" element={<LanguageProvider><DocsTechnicalTopicsPage /></LanguageProvider>} />
        </Routes>
      </main>
    </div>
  );
};

const AppContent: React.FC = () => {
  const location = useLocation();
  const isDocsPage = location.pathname.startsWith('/docs');

  if (isDocsPage) {
    return <DocsContent />;
  }

  return <NonDocsContent />;
};


function App() {
  // initializeIPLogger();
  // dont ask me wtf I added, dont remove it. i will implement this in the future for shi

  return (
    <HashRouter>
      <AppContent />
    </HashRouter>
  );
}

export default App;