/**
 * Language Detection Utility
 * 
 * Automatically detects user's preferred language based on browser settings
 * and provides fallback logic for unsupported languages.
 */

export type SupportedLanguage = 'en' | 'ja';

interface LanguageDetectionResult {
  detectedLanguage: SupportedLanguage;
  isAutoDetected: boolean;
  browserLanguages: readonly string[];
}

/**
 * Gets the user's browser language preferences
 */
export const getBrowserLanguages = (): readonly string[] => {
  // Get all browser language preferences in order of preference
  const languages: string[] = [];
  
  // navigator.languages (most preferred, array of languages)
  if (navigator.languages && navigator.languages.length > 0) {
    languages.push(...navigator.languages);
  }
  
  // navigator.language (single preferred language)
  if (navigator.language) {
    languages.push(navigator.language);
  }
  
  // navigator.userLanguage (IE fallback)
  const userLanguage = (navigator as any).userLanguage;
  if (userLanguage) {
    languages.push(userLanguage);
  }
  
  // Remove duplicates while preserving order
  const uniqueLanguages: string[] = [];
  for (const lang of languages) {
    if (uniqueLanguages.indexOf(lang) === -1) {
      uniqueLanguages.push(lang);
    }
  }
  return uniqueLanguages;
};

/**
 * Normalizes language codes to our supported format
 * Examples: 'en-US' -> 'en', 'ja-JP' -> 'ja', 'fr-FR' -> null
 */
export const normalizeLanguageCode = (languageCode: string): SupportedLanguage | null => {
  if (!languageCode) return null;
  
  // Convert to lowercase and get the primary language code
  const primaryLang = languageCode.toLowerCase().split('-')[0];
  
  switch (primaryLang) {
    case 'en':
      return 'en';
    case 'ja':
      return 'ja';
    default:
      return null;
  }
};

/**
 * Detects the best language to use based on browser preferences
 */
export const detectPreferredLanguage = (): LanguageDetectionResult => {
  const browserLanguages = getBrowserLanguages();
  
  // Try to find a supported language from browser preferences
  for (const browserLang of browserLanguages) {
    const normalized = normalizeLanguageCode(browserLang);
    if (normalized) {
      return {
        detectedLanguage: normalized,
        isAutoDetected: true,
        browserLanguages
      };
    }
  }
  
  // Fallback to English if no supported language found
  return {
    detectedLanguage: 'en',
    isAutoDetected: true,
    browserLanguages
  };
};

/**
 * Gets the initial language preference considering:
 * 1. User's manual choice (stored in localStorage)
 * 2. Auto-detected browser language
 * 3. Default fallback (English)
 */
export const getInitialLanguage = (): {
  language: SupportedLanguage;
  isUserChoice: boolean;
  detectionInfo: LanguageDetectionResult;
} => {
  const STORAGE_KEY = 'lumina-docs-language';
  const USER_CHOICE_KEY = 'lumina-docs-user-choice';
  
  // Check if user has manually set a language preference
  const savedLanguage = localStorage.getItem(STORAGE_KEY);
  const isUserChoice = localStorage.getItem(USER_CHOICE_KEY) === 'true';
  
  // If user has made a manual choice, respect it
  if (isUserChoice && (savedLanguage === 'en' || savedLanguage === 'ja')) {
    const detectionInfo = detectPreferredLanguage();
    return {
      language: savedLanguage,
      isUserChoice: true,
      detectionInfo
    };
  }
  
  // Otherwise, use auto-detection
  const detectionInfo = detectPreferredLanguage();
  
  // Save the auto-detected language but mark it as not user choice
  localStorage.setItem(STORAGE_KEY, detectionInfo.detectedLanguage);
  localStorage.setItem(USER_CHOICE_KEY, 'false');
  
  return {
    language: detectionInfo.detectedLanguage,
    isUserChoice: false,
    detectionInfo
  };
};

/**
 * Saves user's manual language choice
 */
export const saveUserLanguageChoice = (language: SupportedLanguage): void => {
  const STORAGE_KEY = 'lumina-docs-language';
  const USER_CHOICE_KEY = 'lumina-docs-user-choice';
  
  localStorage.setItem(STORAGE_KEY, language);
  localStorage.setItem(USER_CHOICE_KEY, 'true');
};

/**
 * Resets to auto-detection (removes user choice)
 */
export const resetToAutoDetection = (): SupportedLanguage => {
  const USER_CHOICE_KEY = 'lumina-docs-user-choice';
  
  localStorage.removeItem(USER_CHOICE_KEY);
  
  const detectionInfo = detectPreferredLanguage();
  const STORAGE_KEY = 'lumina-docs-language';
  localStorage.setItem(STORAGE_KEY, detectionInfo.detectedLanguage);
  
  return detectionInfo.detectedLanguage;
};


