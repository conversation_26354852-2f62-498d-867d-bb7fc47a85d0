interface IPInfo {
  ip: string;
  country?: string;
  region?: string;
  city?: string;
  timezone?: string;
  userAgent: string;
  timestamp: string;
}

interface LoggerConfig {
  enabled: boolean;
  endpoint?: string;
  includeLocation: boolean;
  retryAttempts: number;
}

const defaultConfig: LoggerConfig = {
  enabled: false,
  endpoint: 'https://ipapi.co/json/',
  includeLocation: true,
  retryAttempts: 3
};

class IPLogger {
  private config: LoggerConfig;
  private loggedIPs: Set<string>;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.loggedIPs = new Set();
  }

  async getIPInfo(): Promise<IPInfo | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      const response = await fetch(this.config.endpoint!);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      const ipInfo: IPInfo = {
        ip: data.ip,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      };

      if (this.config.includeLocation) {
        ipInfo.country = data.country_name;
        ipInfo.region = data.region;
        ipInfo.city = data.city;
        ipInfo.timezone = data.timezone;
      }

      return ipInfo;
    } catch (error) {
      console.error('Failed to fetch IP information:', error);
      return null;
    }
  }

  async logVisitor(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const ipInfo = await this.getIPInfo();
    if (!ipInfo) {
      return;
    }

    if (this.loggedIPs.has(ipInfo.ip)) {
      return;
    }

    this.loggedIPs.add(ipInfo.ip);
    
    try {
      console.log('Visitor logged:', ipInfo);
    } catch (error) {
      console.error('Failed to log visitor:', error);
    }
  }

  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }

  clearLoggedIPs(): void {
    this.loggedIPs.clear();
  }
}

export const ipLogger = new IPLogger();

export const initializeIPLogger = (): void => {
  // ipLogger.setEnabled(true);
  // ipLogger.logVisitor();
};

export default IPLogger;