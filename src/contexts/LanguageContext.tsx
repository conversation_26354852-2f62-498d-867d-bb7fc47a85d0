import React, { createContext, useContext, useState, ReactNode } from 'react';
import {
  getInitialLanguage,
  saveUserLanguageChoice,
  resetToAutoDetection,
  type SupportedLanguage
} from '../utils/languageDetection';

type Language = SupportedLanguage;

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
  isTransitioning: boolean;
  isAutoDetected: boolean;
  resetToAuto: () => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  // Initialize language using detection system
  const [language, setLanguage] = useState<Language>(() => {
    const { language: detectedLanguage } = getInitialLanguage();
    return detectedLanguage;
  });

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isAutoDetected, setIsAutoDetected] = useState(() => {
    const { isUserChoice } = getInitialLanguage();
    return !isUserChoice;
  });

  // Save language preference when user manually changes it
  const handleSetLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    saveUserLanguageChoice(newLanguage);
    setIsAutoDetected(false);
  };

  const toggleLanguage = () => {
    setIsTransitioning(true);

    // Add a small delay to show the transition effect
    setTimeout(() => {
      const newLanguage = language === 'en' ? 'ja' : 'en';
      handleSetLanguage(newLanguage);

      // Reset transition state after content has changed
      setTimeout(() => {
        setIsTransitioning(false);
      }, 100);
    }, 150);
  };

  const resetToAuto = () => {
    const autoDetectedLanguage = resetToAutoDetection();
    setLanguage(autoDetectedLanguage);
    setIsAutoDetected(true);
  };

  const value: LanguageContextType = {
    language,
    setLanguage: handleSetLanguage,
    toggleLanguage,
    isTransitioning,
    isAutoDetected,
    resetToAuto,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
