import React from 'react';
import { Link } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  return (
    <div className="not-found">
      <div className="container">
        <section className="not-found-section">
          <div className="not-found-content">
            <div className="not-found-icon">
              <svg width="120" height="120" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M15 9L9 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 9L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h1 className="not-found-title">Page Not Found</h1>
            <p className="not-found-text">Sorry, the page you are looking for doesn't exist or has been moved.</p>
            <div className="not-found-actions">
              <Link to="/" className="button primary">
                <span>Return Home</span>
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default NotFoundPage; 