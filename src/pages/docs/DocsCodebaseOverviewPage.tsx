import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsCodebaseOverviewPage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'Codebase Overview',
      subtitle: 'Detailed analysis of LuminaV4 project structure and architecture',
      projectStructure: {
        title: 'Project Structure',
        description: 'LuminaV4 follows a modular architecture with clear separation of concerns. The project is organized into several key modules, each serving a specific purpose in the overall application ecosystem.',
        structure: `LuminaV4/
├── app/                    # Main Android application
│   ├── src/main/
│   │   ├── java/com/project/lumina/client/
│   │   │   ├── activity/   # Android activities
│   │   │   ├── application/ # Application context
│   │   │   ├── game/       # Game-related logic
│   │   │   ├── overlay/    # UI overlays
│   │   │   ├── service/    # Background services
│   │   │   └── ui/         # Jetpack Compose UI
│   │   ├── cpp/            # Native C++ components
│   │   └── res/            # Android resources
├── Protocol/               # Minecraft protocol handling
│   ├── bedrock-codec/      # Protocol encoding/decoding
│   ├── bedrock-connection/ # Connection management
│   └── common/             # Shared protocol utilities
├── Network/                # Network communication
│   ├── codec-query/        # Query protocol codec
│   ├── codec-rcon/         # RCON protocol codec
│   └── transport-raknet/   # RakNet transport layer
├── Lunaris/               # Core utility library
├── Pixie/                 # ImGui Android integration
├── SSC/                   # Custom utility dependency
├── TablerIcons/           # Icon library
├── animatedux/            # Animation library
└── minecraft-msftauth/    # Microsoft authentication`
      },
      buildSystem: {
        title: 'Build System',
        description: 'LuminaV4 uses Gradle with Kotlin DSL for build configuration. The build system is configured to support multiple modules, native compilation, and modern Android development practices.',
        mainBuildFile: `plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.lombok)
    kotlin("plugin.serialization") version libs.versions.kotlin
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
}

android {
    namespace = "com.project.lumina.client"
    compileSdk = 35
    
    defaultConfig {
        applicationId = "com.project.lumina.client"
        minSdk = 28
        targetSdk = 35
        versionCode = 2
        versionName = "4.0.3"
        
        ndk {
            abiFilters += setOf("arm64-v8a", "armeabi-v7a")
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
}`,
        settingsFile: `@file:Suppress("UnstableApiUsage")

include(":Lunaris")
include(":Pixie")
include("SSC")
include(":TablerIcons")

rootProject.name = "Lumina v4"
include(":app",
    ":Protocol:bedrock-codec",
    ":Protocol:bedrock-connection",
    ":Protocol:common",
    ":Network:codec-query",
    ":Network:codec-rcon",
    ":Network:transport-raknet",
    ":minecraft-msftauth",
    ":animatedux")`
      },
      coreComponents: {
        title: 'Core Components',
        description: 'The application consists of several core components that work together to provide the complete Lumina experience.',
        components: [
          {
            name: 'Application Context',
            description: 'Central application context managing global state and initialization',
            file: 'com.project.lumina.client.application.AppContext'
          },
          {
            name: 'Activity Management',
            description: 'Multiple activities handling different application states and user interactions',
            activities: [
              'LaunchActivity - Main entry point',
              'MainActivity - Primary application interface',
              'MinecraftCheckActivity - Minecraft version validation',
              'VersionCheckerActivity - Version compatibility checks'
            ]
          },
          {
            name: 'Service Layer',
            description: 'Background services for continuous operation and system integration',
            file: 'com.project.lumina.client.service.Services'
          },
          {
            name: 'UI Framework',
            description: 'Jetpack Compose-based modern UI with Material 3 design system'
          }
        ]
      },
      dependencies: {
        title: 'Key Dependencies',
        description: 'LuminaV4 leverages several key libraries and frameworks to provide its functionality.',
        categories: [
          {
            name: 'Android Framework',
            deps: [
              'Jetpack Compose - Modern UI toolkit',
              'Material 3 - Design system components',
              'AndroidX libraries - Core Android functionality',
              'Firebase - Analytics and crash reporting'
            ]
          },
          {
            name: 'Networking',
            deps: [
              'Netty - High-performance network framework',
              'OkHttp - HTTP client library',
              'CloudBurst Protocol - Minecraft protocol implementation'
            ]
          },
          {
            name: 'Utilities',
            deps: [
              'Gson - JSON serialization',
              'Guava - Google core libraries',
              'LevelDB - Fast key-value storage',
              'Fastutil - High-performance collections'
            ]
          },
          {
            name: 'Security & Auth',
            deps: [
              'Bouncycastle - Cryptographic library',
              'Jose4j - JWT support',
              'Microsoft Auth - Xbox Live authentication'
            ]
          }
        ]
      }
    },
    ja: {
      title: 'コードベース概要',
      subtitle: 'LuminaV4プロジェクト構造とアーキテクチャの詳細分析',
      projectStructure: {
        title: 'プロジェクト構造',
        description: 'LuminaV4は関心の明確な分離を持つモジュラーアーキテクチャに従います。プロジェクトは複数の主要モジュールに整理され、それぞれが全体的なアプリケーションエコシステムで特定の目的を果たします。',
        structure: `LuminaV4/
├── app/                    # メインAndroidアプリケーション
│   ├── src/main/
│   │   ├── java/com/project/lumina/client/
│   │   │   ├── activity/   # Androidアクティビティ
│   │   │   ├── application/ # アプリケーションコンテキスト
│   │   │   ├── game/       # ゲーム関連ロジック
│   │   │   ├── overlay/    # UIオーバーレイ
│   │   │   ├── service/    # バックグラウンドサービス
│   │   │   └── ui/         # Jetpack Compose UI
│   │   ├── cpp/            # ネイティブC++コンポーネント
│   │   └── res/            # Androidリソース
├── Protocol/               # Minecraftプロトコル処理
│   ├── bedrock-codec/      # プロトコルエンコード/デコード
│   ├── bedrock-connection/ # 接続管理
│   └── common/             # 共有プロトコルユーティリティ
├── Network/                # ネットワーク通信
│   ├── codec-query/        # クエリプロトコルコーデック
│   ├── codec-rcon/         # RCONプロトコルコーデック
│   └── transport-raknet/   # RakNetトランスポート層
├── Lunaris/               # コアユーティリティライブラリ
├── Pixie/                 # ImGui Android統合
├── SSC/                   # カスタムユーティリティ依存関係
├── TablerIcons/           # アイコンライブラリ
├── animatedux/            # アニメーションライブラリ
└── minecraft-msftauth/    # Microsoft認証`
      },
      buildSystem: {
        title: 'ビルドシステム',
        description: 'LuminaV4はビルド設定にKotlin DSLを使用したGradleを使用します。ビルドシステムは複数のモジュール、ネイティブコンパイル、モダンAndroid開発手法をサポートするように設定されています。',
        mainBuildFile: `plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.lombok)
    kotlin("plugin.serialization") version libs.versions.kotlin
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
}

android {
    namespace = "com.project.lumina.client"
    compileSdk = 35
    
    defaultConfig {
        applicationId = "com.project.lumina.client"
        minSdk = 28
        targetSdk = 35
        versionCode = 2
        versionName = "4.0.3"
        
        ndk {
            abiFilters += setOf("arm64-v8a", "armeabi-v7a")
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
}`,
        settingsFile: `@file:Suppress("UnstableApiUsage")

include(":Lunaris")
include(":Pixie")
include("SSC")
include(":TablerIcons")

rootProject.name = "Lumina v4"
include(":app",
    ":Protocol:bedrock-codec",
    ":Protocol:bedrock-connection",
    ":Protocol:common",
    ":Network:codec-query",
    ":Network:codec-rcon",
    ":Network:transport-raknet",
    ":minecraft-msftauth",
    ":animatedux")`
      },
      coreComponents: {
        title: 'コアコンポーネント',
        description: 'アプリケーションは、完全なLumina体験を提供するために連携する複数のコアコンポーネントで構成されています。',
        components: [
          {
            name: 'アプリケーションコンテキスト',
            description: 'グローバル状態と初期化を管理する中央アプリケーションコンテキスト',
            file: 'com.project.lumina.client.application.AppContext'
          },
          {
            name: 'アクティビティ管理',
            description: '異なるアプリケーション状態とユーザーインタラクションを処理する複数のアクティビティ',
            activities: [
              'LaunchActivity - メインエントリーポイント',
              'MainActivity - プライマリアプリケーションインターフェース',
              'MinecraftCheckActivity - Minecraftバージョン検証',
              'VersionCheckerActivity - バージョン互換性チェック'
            ]
          },
          {
            name: 'サービス層',
            description: '継続的な動作とシステム統合のためのバックグラウンドサービス',
            file: 'com.project.lumina.client.service.Services'
          },
          {
            name: 'UIフレームワーク',
            description: 'Material 3デザインシステムを使用したJetpack ComposeベースのモダンUI'
          }
        ]
      },
      dependencies: {
        title: '主要依存関係',
        description: 'LuminaV4は機能を提供するために複数の主要ライブラリとフレームワークを活用します。',
        categories: [
          {
            name: 'Androidフレームワーク',
            deps: [
              'Jetpack Compose - モダンUIツールキット',
              'Material 3 - デザインシステムコンポーネント',
              'AndroidXライブラリ - コアAndroid機能',
              'Firebase - 分析とクラッシュレポート'
            ]
          },
          {
            name: 'ネットワーキング',
            deps: [
              'Netty - 高性能ネットワークフレームワーク',
              'OkHttp - HTTPクライアントライブラリ',
              'CloudBurst Protocol - Minecraftプロトコル実装'
            ]
          },
          {
            name: 'ユーティリティ',
            deps: [
              'Gson - JSONシリアライゼーション',
              'Guava - Googleコアライブラリ',
              'LevelDB - 高速キー値ストレージ',
              'Fastutil - 高性能コレクション'
            ]
          },
          {
            name: 'セキュリティ & 認証',
            deps: [
              'Bouncycastle - 暗号化ライブラリ',
              'Jose4j - JWTサポート',
              'Microsoft Auth - Xbox Live認証'
            ]
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.projectStructure.title}</h2>
            <p>{t.projectStructure.description}</p>
            <CodeBlock 
              code={t.projectStructure.structure}
              language="text"
              title="Project Directory Structure"
            />
          </section>

          <section className="docs-section">
            <h2>{t.buildSystem.title}</h2>
            <p>{t.buildSystem.description}</p>
            
            <h3>Main Build Configuration</h3>
            <CodeBlock 
              code={t.buildSystem.mainBuildFile}
              language="gradle"
              title="app/build.gradle.kts"
            />
            
            <h3>Settings Configuration</h3>
            <CodeBlock 
              code={t.buildSystem.settingsFile}
              language="gradle"
              title="settings.gradle.kts"
            />
          </section>

          <section className="docs-section">
            <h2>{t.coreComponents.title}</h2>
            <p>{t.coreComponents.description}</p>
            
            <div className="module-list">
              {t.coreComponents.components.map((component, index) => (
                <div key={index} className="module-item">
                  <h4>{component.name}</h4>
                  <p>{component.description}</p>
                  {component.file && (
                    <CodeBlock 
                      code={component.file}
                      language="text"
                      showLineNumbers={false}
                    />
                  )}
                  {component.activities && (
                    <ul>
                      {component.activities.map((activity, actIndex) => (
                        <li key={actIndex}>{activity}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.dependencies.title}</h2>
            <p>{t.dependencies.description}</p>
            
            <div className="feature-grid">
              {t.dependencies.categories.map((category, index) => (
                <div key={index} className="feature-item">
                  <h3>{category.name}</h3>
                  <ul>
                    {category.deps.map((dep, depIndex) => (
                      <li key={depIndex}>{dep}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsCodebaseOverviewPage;
