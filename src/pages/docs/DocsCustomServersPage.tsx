import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsCustomServersPage: React.FC = () => {
  const { language } = useLanguage();

  const content = {
    en: {
      title: 'Adding Custom Servers',
      subtitle: 'Learn how to add and manage custom Minecraft servers in LuminaV4',
      introduction: {
        title: 'Custom Server Management',
        description: 'LuminaV4 allows you to easily add custom Minecraft servers to connect to your favorite communities, private servers, or testing environments. The built-in Server Manager provides a simple interface to configure and manage multiple server connections.',
        benefits: [
          'Connect to any Minecraft Bedrock server',
          'Save multiple server configurations',
          'Quick server switching and management',
          'Support for custom ports and IP addresses',
          'Persistent server list across sessions',
          'Easy server configuration editing'
        ]
      },
      quickGuide: {
        title: 'Quick Setup Guide',
        description: 'Follow these simple steps to add a custom server to LuminaV4.',
        steps: [
          'Launch LuminaV4',
          'Click "Mobile Client" mode',
          'Go to Settings section in the navigation bar above',
          'Click on "Config" in the "Server Manager" card',
          'Enter the IP Address & Port of the server & click "Save"',
          'Done!'
        ]
      },
      detailedGuide: {
        title: 'Detailed Setup Instructions',
        description: 'Comprehensive step-by-step guide with additional configuration options.',
        steps: [
          {
            step: '1. Launch LuminaV4',
            description: 'Start the LuminaV4 application on your Android device',
            details: [
              'Ensure LuminaV4 is properly installed and updated',
              'Launch the app from your device\'s app drawer',
              'Wait for the application to fully load',
              'Verify you\'re on the main screen with mode selection'
            ]
          },
          {
            step: '2. Select Mobile Client Mode',
            description: 'Choose the Mobile Client mode for direct device usage',
            details: [
              'Tap on "Mobile Client" button on the main screen',
              'This mode allows direct connection to servers from your device',
              'The interface will switch to the mobile client layout',
              'You should see the navigation bar at the top'
            ]
          },
          {
            step: '3. Navigate to Settings',
            description: 'Access the settings section from the navigation bar',
            details: [
              'Look for the navigation bar at the top of the screen',
              'Tap on "Settings" in the navigation bar',
              'This will open the settings panel with various configuration options',
              'You should see multiple configuration cards including "Server Manager"'
            ]
          },
          {
            step: '4. Open Server Manager',
            description: 'Access the Server Manager configuration',
            details: [
              'Locate the "Server Manager" card in the settings panel',
              'Tap on "Config" button within the Server Manager card',
              'This opens the server configuration interface',
              'You\'ll see options to add, edit, and manage servers'
            ]
          },
          {
            step: '5. Add Server Details',
            description: 'Enter your custom server information',
            details: [
              'Enter the server IP address in the designated field',
              'Add the server port (default is 19132 for Minecraft Bedrock)',
              'Optionally, add a server name for easy identification',
              'Verify the information is correct before saving'
            ]
          },
          {
            step: '6. Save Configuration',
            description: 'Save your server configuration',
            details: [
              'Click the "Save" button to store the server configuration',
              'The server will be added to your server list',
              'You can now select this server for connections',
              'The configuration is automatically saved for future use'
            ]
          }
        ]
      },
      serverTypes: {
        title: 'Supported Server Types',
        description: 'LuminaV4 supports various types of Minecraft Bedrock servers.',
        types: [
          {
            name: 'Public Servers',
            description: 'Large community servers with open access',
            examples: ['CubeCraft', 'The Hive'],
            requirements: [
              'Server IP address and port',
              'Stable internet connection',
              'Compatible Minecraft version'
            ]
          }
        ]
      },
      troubleshooting: {
        title: 'Troubleshooting',
        description: 'Common issues when adding custom servers and their solutions.',
        issues: [
          {
            problem: 'Cannot Connect to Server',
            symptoms: ['Connection timeout', 'Server not responding', 'Failed to connect'],
            solutions: [
              'Verify the server IP address and port are correct',
              'Check if the server is online and accessible',
              'Ensure your internet connection is stable',
              'Try connecting from a different network',
              'Contact the server administrator for assistance'
            ]
          },
          {
            problem: 'Server Not Appearing in List',
            symptoms: ['Server not saved', 'Configuration lost', 'Empty server list'],
            solutions: [
              'Make sure you clicked "Save" after entering server details',
              'Check if the app has proper storage permissions',
              'Restart the application and try again',
              'Verify the server information was entered correctly',
              'Clear app cache if the issue persists'
            ]
          },
          {
            problem: 'Invalid Server Address',
            symptoms: ['Invalid IP format', 'Port out of range', 'Address not recognized'],
            solutions: [
              'Ensure IP address follows correct format (e.g., *************)',
              'Verify port number is between 1-65535',
              'Check for typos in the server address',
              'Use numeric IP instead of domain name if issues persist',
              'Confirm the server supports Minecraft Bedrock Edition'
            ]
          }
        ]
      }
    },
    ja: {
      title: 'カスタムサーバーの追加',
      subtitle: 'LuminaV4でカスタムMinecraftサーバーを追加・管理する方法を学ぶ',
      introduction: {
        title: 'カスタムサーバー管理',
        description: 'LuminaV4では、お気に入りのコミュニティ、プライベートサーバー、またはテスト環境に簡単にカスタムMinecraftサーバーを追加できます。内蔵のサーバーマネージャーは、複数のサーバー接続を設定・管理するためのシンプルなインターフェースを提供します。',
        benefits: [
          '任意のMinecraft Bedrockサーバーに接続',
          '複数のサーバー設定を保存',
          'クイックサーバー切り替えと管理',
          'カスタムポートとIPアドレスのサポート',
          'セッション間でのサーバーリスト永続化',
          '簡単なサーバー設定編集'
        ]
      },
      quickGuide: {
        title: 'クイックセットアップガイド',
        description: 'LuminaV4にカスタムサーバーを追加するための簡単な手順に従ってください。',
        steps: [
          'LuminaV4を起動',
          '「モバイルクライアント」モードをクリック',
          '上部のナビゲーションバーの設定セクションに移動',
          '「サーバーマネージャー」カードの「設定」をクリック',
          'サーバーのIPアドレスとポートを入力し、「保存」をクリック',
          '完了！'
        ]
      },
      detailedGuide: {
        title: '詳細セットアップ手順',
        description: '追加の設定オプションを含む包括的なステップバイステップガイド。',
        steps: [
          {
            step: '1. LuminaV4を起動',
            description: 'AndroidデバイスでLuminaV4アプリケーションを開始',
            details: [
              'LuminaV4が適切にインストールされ、更新されていることを確認',
              'デバイスのアプリドロワーからアプリを起動',
              'アプリケーションが完全に読み込まれるまで待機',
              'モード選択のあるメイン画面にいることを確認'
            ]
          },
          {
            step: '2. モバイルクライアントモードを選択',
            description: 'デバイス直接使用のためのモバイルクライアントモードを選択',
            details: [
              'メイン画面の「モバイルクライアント」ボタンをタップ',
              'このモードではデバイスから直接サーバーに接続可能',
              'インターフェースがモバイルクライアントレイアウトに切り替わる',
              '上部にナビゲーションバーが表示されるはず'
            ]
          },
          {
            step: '3. 設定に移動',
            description: 'ナビゲーションバーから設定セクションにアクセス',
            details: [
              '画面上部のナビゲーションバーを探す',
              'ナビゲーションバーの「設定」をタップ',
              'これにより様々な設定オプションを含む設定パネルが開く',
              '「サーバーマネージャー」を含む複数の設定カードが表示されるはず'
            ]
          },
          {
            step: '4. サーバーマネージャーを開く',
            description: 'サーバーマネージャー設定にアクセス',
            details: [
              '設定パネルで「サーバーマネージャー」カードを見つける',
              'サーバーマネージャーカード内の「設定」ボタンをタップ',
              'これによりサーバー設定インターフェースが開く',
              'サーバーの追加、編集、管理のオプションが表示される'
            ]
          },
          {
            step: '5. サーバー詳細を追加',
            description: 'カスタムサーバー情報を入力',
            details: [
              '指定されたフィールドにサーバーIPアドレスを入力',
              'サーバーポートを追加（Minecraft Bedrockのデフォルトは19132）',
              'オプションで、簡単な識別のためのサーバー名を追加',
              '保存前に情報が正しいことを確認'
            ]
          },
          {
            step: '6. 設定を保存',
            description: 'サーバー設定を保存',
            details: [
              '「保存」ボタンをクリックしてサーバー設定を保存',
              'サーバーがサーバーリストに追加される',
              'この接続用にサーバーを選択できるようになる',
              '設定は将来の使用のために自動的に保存される'
            ]
          }
        ]
      },
      serverTypes: {
        title: 'サポートされるサーバータイプ',
        description: 'LuminaV4は様々なタイプのMinecraft Bedrockサーバーをサポートします。',
        types: [
          {
            name: 'パブリックサーバー',
            description: 'オープンアクセスの大規模コミュニティサーバー',
            examples: ['Hypixel Bedrock', 'CubeCraft', 'The Hive'],
            requirements: [
              'サーバーIPアドレスとポート',
              '安定したインターネット接続',
              '互換性のあるMinecraftバージョン'
            ]
          }
        ]
      },
      troubleshooting: {
        title: 'トラブルシューティング',
        description: 'カスタムサーバー追加時の一般的な問題とその解決策。',
        issues: [
          {
            problem: 'サーバーに接続できない',
            symptoms: ['接続タイムアウト', 'サーバーが応答しない', '接続に失敗'],
            solutions: [
              'サーバーIPアドレスとポートが正しいことを確認',
              'サーバーがオンラインでアクセス可能かチェック',
              'インターネット接続が安定していることを確認',
              '別のネットワークから接続を試す',
              'サーバー管理者に支援を求める'
            ]
          },
          {
            problem: 'サーバーがリストに表示されない',
            symptoms: ['サーバーが保存されない', '設定が失われる', '空のサーバーリスト'],
            solutions: [
              'サーバー詳細入力後に「保存」をクリックしたことを確認',
              'アプリに適切なストレージ権限があるかチェック',
              'アプリケーションを再起動して再試行',
              'サーバー情報が正しく入力されたことを確認',
              '問題が続く場合はアプリキャッシュをクリア'
            ]
          },
          {
            problem: '無効なサーバーアドレス',
            symptoms: ['無効なIP形式', 'ポート範囲外', 'アドレスが認識されない'],
            solutions: [
              'IPアドレスが正しい形式に従っていることを確認（例：*************）',
              'ポート番号が1-65535の範囲内であることを確認',
              'サーバーアドレスのタイプミスをチェック',
              '問題が続く場合はドメイン名の代わりに数値IPを使用',
              'サーバーがMinecraft Bedrock Editionをサポートしていることを確認'
            ]
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.introduction.title}</h2>
            <p>{t.introduction.description}</p>
            
            <h3>{language === 'en' ? 'Benefits' : '利点'}</h3>
            <ul>
              {t.introduction.benefits.map((benefit, index) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </section>

          <section className="docs-section">
            <h2>{t.quickGuide.title}</h2>
            <p>{t.quickGuide.description}</p>
            
            <div className="docs-callout success">
              <h4>{language === 'en' ? 'Quick Steps' : 'クイック手順'}</h4>
              <ol>
                {t.quickGuide.steps.map((step, index) => (
                  <li key={index}>{step}</li>
                ))}
              </ol>
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.detailedGuide.title}</h2>
            <p>{t.detailedGuide.description}</p>
            
            {t.detailedGuide.steps.map((step, index) => (
              <div key={index} className="module-item">
                <h4>{step.step}</h4>
                <p>{step.description}</p>
                <ul>
                  {step.details.map((detail, detailIndex) => (
                    <li key={detailIndex}>{detail}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.serverTypes.title}</h2>
            <p>{t.serverTypes.description}</p>
            
            <div className="feature-grid">
              {t.serverTypes.types.map((type, index) => (
                <div key={index} className="feature-item">
                  <h3>{type.name}</h3>
                  <p>{type.description}</p>
                  <div className="category-examples">
                    <h5>{language === 'en' ? 'Examples:' : '例:'}</h5>
                    <div className="example-tags">
                      {type.examples.map((example, exIndex) => (
                        <span key={exIndex} className="example-tag">{example}</span>
                      ))}
                    </div>
                  </div>
                  <div className="category-purpose">
                    <h5>{language === 'en' ? 'Requirements:' : '要件:'}</h5>
                    <ul>
                      {type.requirements.map((req, reqIndex) => (
                        <li key={reqIndex}>{req}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.troubleshooting.title}</h2>
            <p>{t.troubleshooting.description}</p>
            
            {t.troubleshooting.issues.map((issue, index) => (
              <div key={index} className="docs-callout warning">
                <h4>{issue.problem}</h4>
                <h5>{language === 'en' ? 'Symptoms:' : '症状:'}</h5>
                <ul>
                  {issue.symptoms.map((symptom, symptomIndex) => (
                    <li key={symptomIndex}>{symptom}</li>
                  ))}
                </ul>
                <h5>{language === 'en' ? 'Solutions:' : '解決策:'}</h5>
                <ul>
                  {issue.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex}>{solution}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Best Practices' : 'ベストプラクティス'}</h2>
            <div className="docs-callout info">
              <h4>{language === 'en' ? 'Server Configuration Tips' : 'サーバー設定のヒント'}</h4>
              <ul>
                <li>{language === 'en' ? 'Always verify server information before saving' : '保存前に常にサーバー情報を確認'}</li>
                <li>{language === 'en' ? 'Use descriptive names for easy server identification' : '簡単なサーバー識別のために説明的な名前を使用'}</li>
                <li>{language === 'en' ? 'Test connections immediately after adding servers' : 'サーバー追加後すぐに接続をテスト'}</li>
                <li>{language === 'en' ? 'Keep server information updated if addresses change' : 'アドレスが変更された場合はサーバー情報を更新'}</li>
                <li>{language === 'en' ? 'Remove unused servers to keep the list organized' : 'リストを整理するために未使用サーバーを削除'}</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsCustomServersPage;
