import React from 'react';
import { Link } from 'react-router-dom';
import DocsLayout from '../../components/docs/DocsLayout';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsOverviewPage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'LuminaV4 Documentation',
      subtitle: 'Comprehensive guide to the Lumina Client codebase and architecture',
      description: 'Welcome to the LuminaV4 documentation. This comprehensive guide covers the architecture, modules, and implementation details of the Lumina Client - a sophisticated Minecraft Bedrock Edition client designed for competitive PvP gameplay.',
      whatIsLumina: {
        title: 'What is LuminaV4?',
        content: [
          'LuminaV4 is a sophisticated Android-based client for Minecraft Bedrock Edition, specifically designed to enhance competitive PvP gameplay. Built with modern Android development practices using Kotlin and Jetpack Compose, it provides a comprehensive suite of tools and features for Minecraft players.',
          'The project represents a significant evolution in Minecraft client development, incorporating advanced networking protocols, native performance optimizations, and a modern user interface. It supports multiple Minecraft versions and provides extensive customization options for players.',
          'As an open-source project licensed under GNU GPL v3.0, LuminaV4 serves both as a functional client and an educational resource for developers interested in Minecraft protocol implementation and Android development.'
        ]
      },
      keyFeatures: {
        title: 'Key Features',
        features: [
          {
            title: 'Multi-Version Protocol Support',
            description: 'Supports Minecraft Bedrock versions from 1.7.0 to 1.21.50+ with automatic protocol detection and adaptation.'
          },
          {
            title: 'Modern Android Architecture',
            description: 'Built with Jetpack Compose, Material 3 design, and follows Android architecture best practices.'
          },
          {
            title: 'High-Performance Networking',
            description: 'Utilizes Netty framework for efficient packet handling and CloudBurst protocol for Minecraft communication.'
          },
          {
            title: 'Native Performance',
            description: 'Includes C++ components for performance-critical operations and ImGui integration for advanced overlays.'
          },
          {
            title: 'Cross-Platform Compatibility',
            description: 'Primary Android support with remote capabilities for other platforms through network connectivity.'
          },
          {
            title: 'Extensive Customization',
            description: 'Comprehensive configuration options, custom UI themes, and modular architecture for easy extension.'
          }
        ]
      },
      architecture: {
        title: 'Architecture Overview',
        description: 'LuminaV4 follows a modular architecture with clear separation of concerns:',
        modules: [
          {
            name: 'Main Application (app/)',
            description: 'Core Android application with Jetpack Compose UI and activity management'
          },
          {
            name: 'Protocol Module',
            description: 'Handles Minecraft Bedrock protocol communication across multiple versions'
          },
          {
            name: 'Network Module',
            description: 'Manages RakNet transport, query/RCON codecs, and network communication'
          },
          {
            name: 'Lunaris',
            description: 'Core utility library providing essential functionality and helpers'
          },
          {
            name: 'Pixie',
            description: 'ImGui integration for Android with native surface rendering'
          },
          {
            name: 'AnimatedUX',
            description: 'Custom animation library for dynamic UI transitions'
          }
        ]
      },
      gettingStarted: {
        title: 'Getting Started',
        description: 'Explore the documentation sections to learn about different aspects of LuminaV4:',
        sections: [
          {
            title: 'Codebase Overview',
            description: 'Detailed analysis of the project structure, build system, and core components',
            link: '/docs/codebase-overview'
          },
          {
            title: 'Module Documentation',
            description: 'In-depth documentation of each module and their interactions',
            link: '/docs/module-documentation'
          },
          {
            title: 'Module Development',
            description: 'Learn how to create custom modules using CloudBurstMC patterns',
            link: '/docs/module-development'
          },
          {
            title: 'RemoteLink System',
            description: 'Multi-device support through Wi-Fi server hosting and remote control',
            link: '/docs/remotelink-system'
          },
          {
            title: 'Game Modules',
            description: 'Comprehensive guide to LuminaV4\'s modular game enhancement system',
            link: '/docs/game-modules'
          },
          {
            title: 'Custom Servers',
            description: 'Learn how to add and manage custom Minecraft servers',
            link: '/docs/custom-servers'
          },
          {
            title: 'Application Usage',
            description: 'Guide on how to use the Lumina client application effectively',
            link: '/docs/application-usage'
          },
          {
            title: 'Integration Guides',
            description: 'How various components integrate within the application',
            link: '/docs/integration-guides'
          },
          {
            title: 'Technical Topics',
            description: 'Development setup, build process, and advanced technical details',
            link: '/docs/technical-topics'
          }
        ]
      }
    },
    ja: {
      title: 'LuminaV4 ドキュメント',
      subtitle: 'Lumina Clientのコードベースとアーキテクチャの包括的ガイド',
      description: 'LuminaV4ドキュメントへようこそ。この包括的なガイドでは、競技PvPゲームプレイ用に設計された洗練されたMinecraft Bedrock Editionクライアント、Lumina Clientのアーキテクチャ、モジュール、実装詳細について説明します。',
      whatIsLumina: {
        title: 'LuminaV4とは？',
        content: [
          'LuminaV4は、競技PvPゲームプレイを強化するために特別に設計された、Minecraft Bedrock Edition用の洗練されたAndroidベースのクライアントです。KotlinとJetpack Composeを使用した最新のAndroid開発手法で構築され、Minecraftプレイヤー向けの包括的なツールと機能スイートを提供します。',
          'このプロジェクトは、高度なネットワークプロトコル、ネイティブパフォーマンス最適化、モダンユーザーインターフェースを組み込んだ、Minecraftクライアント開発の大幅な進化を表しています。複数のMinecraftバージョンをサポートし、プレイヤーに広範なカスタマイズオプションを提供します。',
          'GNU GPL v3.0の下でライセンスされたオープンソースプロジェクトとして、LuminaV4は機能的なクライアントとしてだけでなく、Minecraftプロトコル実装とAndroid開発に興味のある開発者向けの教育リソースとしても機能します。'
        ]
      },
      keyFeatures: {
        title: '主要機能',
        features: [
          {
            title: 'マルチバージョンプロトコルサポート',
            description: 'Minecraft Bedrockバージョン1.7.0から1.21.50+まで、自動プロトコル検出と適応をサポート。'
          },
          {
            title: 'モダンAndroidアーキテクチャ',
            description: 'Jetpack Compose、Material 3デザインで構築され、Androidアーキテクチャのベストプラクティスに従っています。'
          },
          {
            title: '高性能ネットワーキング',
            description: '効率的なパケット処理にNettyフレームワーク、Minecraft通信にCloudBurstプロトコルを利用。'
          },
          {
            title: 'ネイティブパフォーマンス',
            description: 'パフォーマンス重要な操作用のC++コンポーネントと、高度なオーバーレイ用のImGui統合を含む。'
          },
          {
            title: 'クロスプラットフォーム互換性',
            description: 'プライマリAndroidサポートと、ネットワーク接続を通じた他プラットフォーム向けリモート機能。'
          },
          {
            title: '広範なカスタマイズ',
            description: '包括的な設定オプション、カスタムUIテーマ、簡単な拡張のためのモジュラーアーキテクチャ。'
          }
        ]
      },
      architecture: {
        title: 'アーキテクチャ概要',
        description: 'LuminaV4は関心の明確な分離を持つモジュラーアーキテクチャに従います：',
        modules: [
          {
            name: 'メインアプリケーション (app/)',
            description: 'Jetpack Compose UIとアクティビティ管理を持つコアAndroidアプリケーション'
          },
          {
            name: 'プロトコルモジュール',
            description: '複数バージョンにわたるMinecraft Bedrockプロトコル通信を処理'
          },
          {
            name: 'ネットワークモジュール',
            description: 'RakNetトランスポート、クエリ/RCONコーデック、ネットワーク通信を管理'
          },
          {
            name: 'Lunaris',
            description: '必須機能とヘルパーを提供するコアユーティリティライブラリ'
          },
          {
            name: 'Pixie',
            description: 'ネイティブサーフェスレンダリングを持つAndroid用ImGui統合'
          },
          {
            name: 'AnimatedUX',
            description: '動的UI遷移用のカスタムアニメーションライブラリ'
          }
        ]
      },
      gettingStarted: {
        title: '始めに',
        description: 'LuminaV4の異なる側面について学ぶために、ドキュメントセクションを探索してください：',
        sections: [
          {
            title: 'コードベース概要',
            description: 'プロジェクト構造、ビルドシステム、コアコンポーネントの詳細分析',
            link: '/docs/codebase-overview'
          },
          {
            title: 'モジュール文書',
            description: '各モジュールとその相互作用の詳細ドキュメント',
            link: '/docs/module-documentation'
          },
          {
            title: 'モジュール開発',
            description: 'CloudBurstMCパターンを使用してカスタムモジュールを作成する方法を学ぶ',
            link: '/docs/module-development'
          },
          {
            title: 'RemoteLinkシステム',
            description: 'Wi-Fiサーバーホスティングとリモートコントロールによるマルチデバイスサポート',
            link: '/docs/remotelink-system'
          },
          {
            title: 'ゲームモジュール',
            description: 'LuminaV4のモジュラーゲーム強化システムの包括的ガイド',
            link: '/docs/game-modules'
          },
          {
            title: 'カスタムサーバー',
            description: 'カスタムMinecraftサーバーの追加と管理方法を学ぶ',
            link: '/docs/custom-servers'
          },
          {
            title: 'アプリケーション使用法',
            description: 'Luminaクライアントアプリケーションを効果的に使用する方法のガイド',
            link: '/docs/application-usage'
          },
          {
            title: '統合ガイド',
            description: 'アプリケーション内での様々なコンポーネントの統合方法',
            link: '/docs/integration-guides'
          },
          {
            title: '技術的トピック',
            description: '開発セットアップ、ビルドプロセス、高度な技術詳細',
            link: '/docs/technical-topics'
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <p className="docs-intro">{t.description}</p>
          </section>

          <section className="docs-section">
            <h2>{t.whatIsLumina.title}</h2>
            {t.whatIsLumina.content.map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.keyFeatures.title}</h2>
            <div className="feature-grid">
              {t.keyFeatures.features.map((feature, index) => (
                <div key={index} className="feature-item">
                  <h3>{feature.title}</h3>
                  <p>{feature.description}</p>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.architecture.title}</h2>
            <p>{t.architecture.description}</p>
            <div className="module-list">
              {t.architecture.modules.map((module, index) => (
                <div key={index} className="module-item">
                  <h4>{module.name}</h4>
                  <p>{module.description}</p>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.gettingStarted.title}</h2>
            <p>{t.gettingStarted.description}</p>
            <div className="section-grid">
              {t.gettingStarted.sections.map((section, index) => (
                <Link key={index} to={section.link} className="section-card">
                  <h3>{section.title}</h3>
                  <p>{section.description}</p>
                  <div className="section-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsOverviewPage;
