import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsRemoteLinkSystemPage: React.FC = () => {
  const { language } = useLanguage();

  const content = {
    en: {
      title: 'RemoteLink System',
      subtitle: 'Multi-device support through Wi-Fi server hosting and remote control',
      introduction: {
        title: 'What is RemoteLink?',
        description: 'RemoteLink is LuminaV4\'s innovative multi-device support system that allows you to control the client remotely from other devices on your network. By hosting a server using your device\'s Wi-Fi IP address and port, you can connect multiple devices and control LuminaV4 features remotely through text commands.',
        features: [
          'Multi-device connectivity over Wi-Fi network',
          'Real-time command execution and feedback',
          'Secure session management with proxy player validation',
          'Comprehensive module control (toggle, configure, monitor)',
          'Cross-platform compatibility (Android, PC, iOS via network)',
          'Low-latency communication for responsive control'
        ]
      },
      architecture: {
        title: 'System Architecture',
        description: 'The RemoteLink system consists of several key components working together to provide seamless remote control capabilities.',
        components: [
          {
            name: 'RemSession (Command Handler)',
            description: 'Core session management and command processing',
            responsibilities: [
              'Text packet interception and parsing',
              'Command validation and execution',
              'Feedback message generation',
              'Module state management'
            ]
          },
          {
            name: 'NetBound (Network Interface)',
            description: 'Network communication and session binding',
            responsibilities: [
              'Client-server connection management',
              'Proxy player validation',
              'Message routing and delivery',
              'Session state synchronization'
            ]
          },
          {
            name: 'GameManager Integration',
            description: 'Module system integration for remote control',
            responsibilities: [
              'Module discovery and enumeration',
              'Real-time module state updates',
              'Configuration value management',
              'Event propagation and handling'
            ]
          }
        ]
      },
      setup: {
        title: 'Setting Up RemoteLink',
        description: 'Follow these steps to configure and use the RemoteLink system for multi-device control.',
        quickStart: {
          title: 'Quick Start Guide',
          description: 'Step-by-step instructions to get RemoteLink working quickly.',
          steps: [
            'Launch LuminaV4',
            'Click "Remote Link"',
            'Make sure in the "Settings" section that you have the Minecraft server you want to connect to selected',
            'Make sure you have the Minecraft account selected in the "Accounts" tab to use',
            'Click "Start Remote Link"',
            'On your other device (PC) use the Wi-Fi IP & port: 19132 to connect to the proxy server (Add it as a server list in your PC Minecraft)',
            'In the "Modules" section, you can control which modules to use from your phone or use commands with the "!" prefix from the chat PC'
          ]
        },
        detailedSteps: [
          {
            step: '1. Network Configuration',
            description: 'Ensure all devices are connected to the same Wi-Fi network',
            details: [
              'Connect your Android device (running LuminaV4) to Wi-Fi',
              'Note down the device\'s IP address (Settings > Wi-Fi > Network Details)',
              'Ensure other devices are on the same network subnet',
              'Check firewall settings allow local network communication'
            ]
          },
          {
            step: '2. LuminaV4 Setup',
            description: 'Configure RemoteLink on your Android device',
            details: [
              'Launch LuminaV4 application',
              'Click "Remote Link" mode',
              'Go to "Settings" section and select your target Minecraft server',
              'Navigate to "Accounts" tab and ensure your Xbox Minecraft account is selected',
              'Click "Start Remote Link" button',
              'Wait for the server to initialize and note the displayed IP:19132'
            ]
          },
          {
            step: '3. Remote Device Connection',
            description: 'Connect your PC or other device to the RemoteLink server',
            details: [
              'Open Minecraft Bedrock Edition on your PC/remote device',
              'Go to "Play" > "Servers" > "Add Server"',
              'Enter the Wi-Fi IP address shown in LuminaV4',
              'Set port to 19132',
              'Save and connect to the server',
              'You should now be connected through the RemoteLink proxy'
            ]
          },
          {
            step: '4. Module Control',
            description: 'Control LuminaV4 modules from your remote device',
            details: [
              'Use the "Modules" section on your phone to toggle features',
              'Alternatively, use chat commands with "!" prefix on PC',
              'Type "!help" in chat to see available commands',
              'Use "!toggle <module>" to enable/disable modules',
              'Use "!set <module> <setting> <value>" to configure modules'
            ]
          }
        ]
      },
      commands: {
        title: 'Command System',
        description: 'RemoteLink uses a text-based command system with the "!" prefix for all remote operations.',
        commandList: [
          {
            command: '!ping',
            description: 'Test server connectivity and response time',
            usage: '!ping',
            example: '!ping',
            response: '✦ Pong! Response from CmdListener'
          },
          {
            command: '!help',
            description: 'Display available modules and commands',
            usage: '!help',
            example: '!help',
            response: 'Shows complete module list with current states and available commands'
          },
          {
            command: '!toggle',
            description: 'Enable or disable a specific module',
            usage: '!toggle <module_name>',
            example: '!toggle KillAura',
            response: '✪ KillAura has been enabled!'
          },
          {
            command: '!set',
            description: 'Configure module settings and values',
            usage: '!set <module> <setting> <value>',
            example: '!set KillAura Range 4.5',
            response: '✪ KillAura.Range set to 4.5!'
          }
        ]
      },
      implementation: {
        title: 'Implementation Details',
        description: 'Understanding the technical implementation of the RemoteLink system.',
        codeExample: `// RemSession.kt - Core command processing
class RemSession(val moduleManager: GameManager) : ComposedPacketHandler {
    private val prefix = "!"
    private val feedback = true

    override fun beforePacketBound(packet: BedrockPacket): Boolean {
        if (packet !is TextPacket) return false

        // Validate proxy player and session
        if (!isSessionCreated || !session.isProxyPlayer(packet.sourceName)) {
            return false
        }

        val message = packet.message.trim()
        if (!message.startsWith(prefix)) return false

        // Parse command and arguments
        val args = message.substring(prefix.length).split(" ")
        if (args.isEmpty()) return false

        val command = args[0].lowercase()

        // Execute command with feedback
        when (command) {
            "toggle" -> handleToggleCommand(args[1])
            "set" -> handleSetCommand(args[1], args[2], args[3])
            "ping" -> sendClientMessage("✦ Pong! Response from CmdListener")
            "help" -> handleHelpCommand()
            else -> sendClientMessage("✗ Unknown command: " + command)
        }

        return false // Allow packet to continue processing
    }

    private fun handleToggleCommand(moduleName: String) {
        val module = moduleManager.getModule(moduleName)
        if (module != null) {
            module.isEnabled = !module.isEnabled
            val state = if (module.isEnabled) "enabled" else "disabled"
            sendClientMessage("✪ " + module.name + " has been " + state + "!")
        } else {
            sendClientMessage("✗ Module '" + moduleName + "' not found.")
        }
    }

    private fun sendClientMessage(message: String) {
        session.displayClientMessage(message, TextPacket.Type.RAW)
    }
}`
      }
    },
    ja: {
      title: 'RemoteLinkシステム',
      subtitle: 'Wi-Fiサーバーホスティングとリモートコントロールによるマルチデバイスサポート',
      introduction: {
        title: 'RemoteLinkとは？',
        description: 'RemoteLinkは、ネットワーク上の他のデバイスからクライアントをリモートで制御できるLuminaV4の革新的なマルチデバイスサポートシステムです。デバイスのWi-Fi IPアドレスとポートを使用してサーバーをホストすることで、複数のデバイスを接続し、テキストコマンドを通じてLuminaV4機能をリモートで制御できます。',
        features: [
          'Wi-Fiネットワーク経由のマルチデバイス接続',
          'リアルタイムコマンド実行とフィードバック',
          'プロキシプレイヤー検証による安全なセッション管理',
          '包括的なモジュール制御（切り替え、設定、監視）',
          'クロスプラットフォーム互換性（Android、PC、iOS via ネットワーク）',
          'レスポンシブ制御のための低遅延通信'
        ]
      },
      architecture: {
        title: 'システムアーキテクチャ',
        description: 'RemoteLinkシステムは、シームレスなリモートコントロール機能を提供するために連携する複数の主要コンポーネントで構成されています。',
        components: [
          {
            name: 'RemSession（コマンドハンドラー）',
            description: 'コアセッション管理とコマンド処理',
            responsibilities: [
              'テキストパケットの傍受と解析',
              'コマンド検証と実行',
              'フィードバックメッセージ生成',
              'モジュール状態管理'
            ]
          },
          {
            name: 'NetBound（ネットワークインターフェース）',
            description: 'ネットワーク通信とセッションバインディング',
            responsibilities: [
              'クライアント-サーバー接続管理',
              'プロキシプレイヤー検証',
              'メッセージルーティングと配信',
              'セッション状態同期'
            ]
          },
          {
            name: 'GameManager統合',
            description: 'リモートコントロール用モジュールシステム統合',
            responsibilities: [
              'モジュール発見と列挙',
              'リアルタイムモジュール状態更新',
              '設定値管理',
              'イベント伝播と処理'
            ]
          }
        ]
      },
      setup: {
        title: 'RemoteLinkのセットアップ',
        description: 'マルチデバイス制御のためのRemoteLinkシステムの設定と使用手順に従ってください。',
        quickStart: {
          title: 'クイックスタートガイド',
          description: 'RemoteLinkを迅速に動作させるためのステップバイステップ手順。',
          steps: [
            'LuminaV4を起動',
            '「Remote Link」をクリック',
            '「設定」セクションで接続したいMinecraftサーバーが選択されていることを確認',
            '「アカウント」タブで使用するMinecraftアカウントが選択されていることを確認',
            '「Start Remote Link」をクリック',
            '他のデバイス（PC）でWi-Fi IPとポート：19132を使用してプロキシサーバーに接続（PC Minecraftのサーバーリストにサーバーとして追加）',
            '「モジュール」セクションで、スマートフォンから使用するモジュールを制御するか、PC チャットから「!」プレフィックス付きコマンドを使用'
          ]
        },
        detailedSteps: [
          {
            step: '1. ネットワーク設定',
            description: 'すべてのデバイスが同じWi-Fiネットワークに接続されていることを確認',
            details: [
              'Androidデバイス（LuminaV4実行中）をWi-Fiに接続',
              'デバイスのIPアドレスをメモ（設定 > Wi-Fi > ネットワーク詳細）',
              '他のデバイスが同じネットワークサブネットにあることを確認',
              'ファイアウォール設定がローカルネットワーク通信を許可していることを確認'
            ]
          },
          {
            step: '2. LuminaV4セットアップ',
            description: 'AndroidデバイスでRemoteLinkを設定',
            details: [
              'LuminaV4アプリケーションを起動',
              '「Remote Link」モードをクリック',
              '「設定」セクションに移動し、ターゲットMinecraftサーバーを選択',
              '「アカウント」タブに移動し、Xbox Minecraftアカウントが選択されていることを確認',
              '「Start Remote Link」ボタンをクリック',
              'サーバーが初期化されるのを待ち、表示されたIP:19132をメモ'
            ]
          },
          {
            step: '3. リモートデバイス接続',
            description: 'PCまたは他のデバイスをRemoteLinkサーバーに接続',
            details: [
              'PC/リモートデバイスでMinecraft Bedrock Editionを開く',
              '「プレイ」>「サーバー」>「サーバーを追加」に移動',
              'LuminaV4に表示されたWi-Fi IPアドレスを入力',
              'ポートを19132に設定',
              '保存してサーバーに接続',
              'RemoteLinkプロキシ経由で接続されているはずです'
            ]
          },
          {
            step: '4. モジュール制御',
            description: 'リモートデバイスからLuminaV4モジュールを制御',
            details: [
              'スマートフォンの「モジュール」セクションを使用して機能を切り替え',
              'または、PCで「!」プレフィックス付きチャットコマンドを使用',
              'チャットで「!help」と入力して利用可能なコマンドを表示',
              '「!toggle <モジュール>」を使用してモジュールを有効/無効化',
              '「!set <モジュール> <設定> <値>」を使用してモジュールを設定'
            ]
          }
        ]
      },
      commands: {
        title: 'コマンドシステム',
        description: 'RemoteLinkは、すべてのリモート操作に「!」プレフィックスを使用するテキストベースのコマンドシステムを使用します。',
        commandList: [
          {
            command: '!ping',
            description: 'サーバー接続と応答時間をテスト',
            usage: '!ping',
            example: '!ping',
            response: '✦ Pong! CmdListenerからの応答'
          },
          {
            command: '!help',
            description: '利用可能なモジュールとコマンドを表示',
            usage: '!help',
            example: '!help',
            response: '現在の状態と利用可能なコマンドを含む完全なモジュールリストを表示'
          },
          {
            command: '!toggle',
            description: '特定のモジュールを有効または無効にする',
            usage: '!toggle <モジュール名>',
            example: '!toggle KillAura',
            response: '✪ KillAuraが有効になりました！'
          },
          {
            command: '!set',
            description: 'モジュール設定と値を構成',
            usage: '!set <モジュール> <設定> <値>',
            example: '!set KillAura Range 4.5',
            response: '✪ KillAura.Rangeが4.5に設定されました！'
          }
        ]
      },
      implementation: {
        title: '実装詳細',
        description: 'RemoteLinkシステムの技術的実装の理解。',
        codeExample: `// RemSession.kt - コアコマンド処理
class RemSession(val moduleManager: GameManager) : ComposedPacketHandler {
    private val prefix = "!"
    private val feedback = true

    override fun beforePacketBound(packet: BedrockPacket): Boolean {
        if (packet !is TextPacket) return false

        // プロキシプレイヤーとセッションを検証
        if (!isSessionCreated || !session.isProxyPlayer(packet.sourceName)) {
            return false
        }

        val message = packet.message.trim()
        if (!message.startsWith(prefix)) return false

        // コマンドと引数を解析
        val args = message.substring(prefix.length).split(" ")
        if (args.isEmpty()) return false

        val command = args[0].lowercase()

        // フィードバック付きでコマンドを実行
        when (command) {
            "toggle" -> handleToggleCommand(args[1])
            "set" -> handleSetCommand(args[1], args[2], args[3])
            "ping" -> sendClientMessage("✦ Pong! CmdListenerからの応答")
            "help" -> handleHelpCommand()
            else -> sendClientMessage("✗ 不明なコマンド: " + command)
        }

        return false // パケットの処理を続行
    }

    private fun handleToggleCommand(moduleName: String) {
        val module = moduleManager.getModule(moduleName)
        if (module != null) {
            module.isEnabled = !module.isEnabled
            val state = if (module.isEnabled) "有効" else "無効"
            sendClientMessage("✪ " + module.name + "が" + state + "になりました！")
        } else {
            sendClientMessage("✗ モジュール'" + moduleName + "'が見つかりません。")
        }
    }

    private fun sendClientMessage(message: String) {
        session.displayClientMessage(message, TextPacket.Type.RAW)
    }
}`
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.introduction.title}</h2>
            <p>{t.introduction.description}</p>
            
            <h3>{language === 'en' ? 'Key Features' : '主要機能'}</h3>
            <ul>
              {t.introduction.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </section>

          <section className="docs-section">
            <h2>{t.architecture.title}</h2>
            <p>{t.architecture.description}</p>
            
            <div className="module-list">
              {t.architecture.components.map((component, index) => (
                <div key={index} className="module-item">
                  <h4>{component.name}</h4>
                  <p>{component.description}</p>
                  <h5>{language === 'en' ? 'Responsibilities:' : '責任:'}</h5>
                  <ul>
                    {component.responsibilities.map((responsibility, respIndex) => (
                      <li key={respIndex}>{responsibility}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.setup.title}</h2>
            <p>{t.setup.description}</p>

            <div className="docs-callout info">
              <h4>{t.setup.quickStart.title}</h4>
              <p>{t.setup.quickStart.description}</p>
              <ol>
                {t.setup.quickStart.steps.map((step, index) => (
                  <li key={index}>{step}</li>
                ))}
              </ol>
            </div>

            <h3>{language === 'en' ? 'Detailed Setup Instructions' : '詳細セットアップ手順'}</h3>
            {t.setup.detailedSteps.map((step, index) => (
              <div key={index}>
                <h4>{step.step}</h4>
                <p>{step.description}</p>
                <ul>
                  {step.details.map((detail, detailIndex) => (
                    <li key={detailIndex}>{detail}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.commands.title}</h2>
            <p>{t.commands.description}</p>
            
            <div className="docs-table-container">
              <table className="docs-table">
                <thead>
                  <tr>
                    <th>{language === 'en' ? 'Command' : 'コマンド'}</th>
                    <th>{language === 'en' ? 'Description' : '説明'}</th>
                    <th>{language === 'en' ? 'Usage' : '使用法'}</th>
                    <th>{language === 'en' ? 'Example' : '例'}</th>
                  </tr>
                </thead>
                <tbody>
                  {t.commands.commandList.map((cmd, index) => (
                    <tr key={index}>
                      <td><code>{cmd.command}</code></td>
                      <td>{cmd.description}</td>
                      <td><code>{cmd.usage}</code></td>
                      <td><code>{cmd.example}</code></td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.implementation.title}</h2>
            <p>{t.implementation.description}</p>
            
            <CodeBlock 
              code={t.implementation.codeExample}
              language="kotlin"
              title="RemSession.kt - Command Processing"
            />
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Network Configuration' : 'ネットワーク設定'}</h2>
            <div className="docs-callout info">
              <h4>{language === 'en' ? 'Network Requirements' : 'ネットワーク要件'}</h4>
              <ul>
                <li>{language === 'en' ? 'All devices must be on the same Wi-Fi network' : 'すべてのデバイスが同じWi-Fiネットワーク上にある必要があります'}</li>
                <li>{language === 'en' ? 'Port 19132 (or configured port) must be accessible' : 'ポート19132（または設定されたポート）がアクセス可能である必要があります'}</li>
                <li>{language === 'en' ? 'Firewall should allow local network traffic' : 'ファイアウォールはローカルネットワークトラフィックを許可する必要があります'}</li>
                <li>{language === 'en' ? 'Stable Wi-Fi connection recommended for best performance' : '最高のパフォーマンスのために安定したWi-Fi接続を推奨'}</li>
              </ul>
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Troubleshooting' : 'トラブルシューティング'}</h2>
            <div className="feature-grid">
              <div className="feature-item">
                <h3>{language === 'en' ? 'Connection Issues' : '接続問題'}</h3>
                <ul>
                  <li>{language === 'en' ? 'Verify all devices are on same network' : 'すべてのデバイスが同じネットワーク上にあることを確認'}</li>
                  <li>{language === 'en' ? 'Check IP address and port configuration' : 'IPアドレスとポート設定を確認'}</li>
                  <li>{language === 'en' ? 'Restart RemoteLink server if needed' : '必要に応じてRemoteLinkサーバーを再起動'}</li>
                </ul>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'Command Not Working' : 'コマンドが動作しない'}</h3>
                <ul>
                  <li>{language === 'en' ? 'Ensure commands start with "!" prefix' : 'コマンドが「!」プレフィックスで始まることを確認'}</li>
                  <li>{language === 'en' ? 'Check module names are spelled correctly' : 'モジュール名が正しく綴られていることを確認'}</li>
                  <li>{language === 'en' ? 'Verify you are connected as proxy player' : 'プロキシプレイヤーとして接続されていることを確認'}</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsRemoteLinkSystemPage;
