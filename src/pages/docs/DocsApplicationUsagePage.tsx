import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsApplicationUsagePage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'Application Usage',
      subtitle: 'Comprehensive guide to using the LuminaV4 client application',
      installation: {
        title: 'Installation & Setup',
        description: 'LuminaV4 is distributed as an Android APK file. Follow these steps to install and configure the client.',
        requirements: [
          'Android 9.0 (API level 28) or higher',
          'ARM64 or ARMv7 processor architecture',
          'At least 200 MB of free storage space',
          'Stable internet connection for multiplayer features',
          'Minecraft Bedrock Edition 1.21.80 or later (recommended)'
        ],
        steps: [
          'Download the latest LuminaV4 APK from the official releases',
          'Enable "Install from Unknown Sources" in Android settings',
          'Install the APK file using a file manager',
          'Launch Lumina and complete the initial setup',
          'Configure your Microsoft account for authentication',
          'Customize settings according to your preferences'
        ]
      },
      usage: {
        title: 'Getting Started',
        description: 'Quick guide to start using LuminaV4 for enhanced Minecraft gameplay.',
        mobileClient: {
          title: 'Mobile Client Mode',
          description: 'Use LuminaV4 directly on your Android device for enhanced Minecraft gameplay.',
          steps: [
            'Launch LuminaV4',
            'Click "Mobile Client" mode',
            'Go to the "Accounts" tab and login with your Xbox Minecraft account',
            'Select a server from the Tab servers selector that you want to connect to',
            'Click on "Start" button (And wait for Minecraft to load)',
            'Go to the Minecraft servers tab and make sure you have the Lumina server listed and connect'
          ]
        },
        remoteLink: {
          title: 'RemoteLink Mode',
          description: 'For detailed RemoteLink setup and usage instructions, please refer to the dedicated RemoteLink System documentation.',
          note: 'RemoteLink allows you to control LuminaV4 remotely from other devices using Wi-Fi connection. See the RemoteLink System documentation for complete setup instructions.'
        }
      },
      userInterface: {
        title: 'User Interface Overview',
        description: 'LuminaV4 features a modern Material 3 design with intuitive navigation and customizable layouts.',
        components: [
          {
            name: 'Main Dashboard',
            description: 'Central hub for accessing all client features and settings',
            features: [
              'Quick server connection',
              'Recent servers list',
              'Feature toggle switches',
              'Performance metrics display'
            ]
          },
          {
            name: 'Settings Panel',
            description: 'Comprehensive configuration interface for all client options',
            categories: [
              'General Settings - Basic client configuration',
              'Performance Settings - Optimization options',
              'Visual Settings - UI customization and themes',
              'Network Settings - Connection and protocol options',
              'Security Settings - Authentication and privacy controls'
            ]
          },
          {
            name: 'Overlay System',
            description: 'In-game overlay providing real-time information and controls',
            features: [
              'Performance monitoring (FPS, ping, memory usage)',
              'Feature status indicators',
              'Quick settings access',
              'Debug information display'
            ]
          }
        ]
      },
      coreFeatures: {
        title: 'Core Features',
        description: 'LuminaV4 provides a comprehensive set of features designed to enhance your Minecraft Bedrock experience.',
        categories: [
          {
            name: 'Connection Management',
            description: 'Advanced server connection and protocol handling',
            features: [
              'Multi-version protocol support',
              'Automatic server detection',
              'Connection optimization',
              'Latency reduction techniques'
            ],
            usage: `// Connect to a server
ServerConnection connection = new ServerConnection();
connection.setAddress("play.example.com");
connection.setPort(19132);
connection.setProtocolVersion(ProtocolVersion.LATEST);
connection.connect();`
          },
          {
            name: 'Performance Optimization',
            description: 'System-level optimizations for improved gameplay',
            features: [
              'Memory management optimization',
              'CPU usage reduction',
              'Network packet optimization',
              'Rendering performance improvements'
            ]
          },
          {
            name: 'Customization Options',
            description: 'Extensive customization capabilities for personalized experience',
            features: [
              'Custom UI themes and colors',
              'Configurable keybindings',
              'Feature enable/disable toggles',
              'Performance profile presets'
            ]
          }
        ]
      },
      configuration: {
        title: 'Configuration Guide',
        description: 'Detailed guide on configuring LuminaV4 for optimal performance and functionality.',
        configFile: `{
  "client": {
    "version": "4.0.3",
    "autoUpdate": true,
    "theme": "dark"
  },
  "network": {
    "protocolVersion": "auto",
    "connectionTimeout": 5000,
    "maxRetries": 3,
    "compression": true
  },
  "performance": {
    "memoryOptimization": true,
    "cpuOptimization": true,
    "renderOptimization": true,
    "backgroundProcessing": false
  },
  "features": {
    "overlay": {
      "enabled": true,
      "position": "top-right",
      "opacity": 0.8
    },
    "monitoring": {
      "fps": true,
      "ping": true,
      "memory": true
    }
  },
  "security": {
    "encryptionEnabled": true,
    "authenticationRequired": true,
    "dataCollection": false
  }
}`,
        sections: [
          {
            name: 'Client Configuration',
            description: 'Basic client settings and preferences',
            options: [
              'version - Client version information',
              'autoUpdate - Automatic update checking',
              'theme - UI theme selection (dark/light)'
            ]
          },
          {
            name: 'Network Configuration',
            description: 'Network and protocol settings',
            options: [
              'protocolVersion - Minecraft protocol version',
              'connectionTimeout - Connection timeout in milliseconds',
              'maxRetries - Maximum connection retry attempts',
              'compression - Enable packet compression'
            ]
          },
          {
            name: 'Performance Configuration',
            description: 'Performance optimization settings',
            options: [
              'memoryOptimization - Enable memory optimization',
              'cpuOptimization - Enable CPU optimization',
              'renderOptimization - Enable rendering optimization',
              'backgroundProcessing - Allow background processing'
            ]
          }
        ]
      },
      troubleshooting: {
        title: 'Troubleshooting',
        description: 'Common issues and their solutions when using LuminaV4.',
        issues: [
          {
            problem: 'Connection Failed',
            symptoms: ['Unable to connect to servers', 'Timeout errors', 'Protocol mismatch warnings'],
            solutions: [
              'Verify server address and port are correct',
              'Check internet connection stability',
              'Ensure Minecraft version compatibility',
              'Try different protocol version settings',
              'Restart the application and try again'
            ]
          },
          {
            problem: 'Performance Issues',
            symptoms: ['Low FPS', 'High memory usage', 'Application lag', 'Slow response times'],
            solutions: [
              'Enable performance optimization in settings',
              'Close unnecessary background applications',
              'Reduce overlay complexity and opacity',
              'Lower graphics settings in Minecraft',
              'Restart device to free up memory'
            ]
          },
          {
            problem: 'Authentication Errors',
            symptoms: ['Login failures', 'Microsoft account errors', 'Token expiration messages'],
            solutions: [
              'Re-authenticate with Microsoft account',
              'Check internet connection during login',
              'Clear application cache and data',
              'Ensure Microsoft account has Xbox Live access',
              'Update the application to latest version'
            ]
          }
        ]
      }
    },
    ja: {
      title: 'アプリケーション使用法',
      subtitle: 'LuminaV4クライアントアプリケーションの使用に関する包括的ガイド',
      installation: {
        title: 'インストール & セットアップ',
        description: 'LuminaV4はAndroid APKファイルとして配布されます。クライアントをインストールして設定するには、以下の手順に従ってください。',
        requirements: [
          'Android 9.0（APIレベル28）以上',
          'ARM64またはARMv7プロセッサアーキテクチャ',
          '最低200MBの空きストレージ容量',
          'マルチプレイヤー機能用の安定したインターネット接続',
          'Minecraft Bedrock Edition 1.21.80以降（推奨）'
        ],
        steps: [
          '公式リリースから最新のLuminaV4 APKをダウンロード',
          'Android設定で「提供元不明のアプリ」を有効化',
          'ファイルマネージャーを使用してAPKファイルをインストール',
          'Luminaを起動して初期セットアップを完了',
          '認証用にMicrosoftアカウントを設定',
          '好みに応じて設定をカスタマイズ'
        ]
      },
      usage: {
        title: '使用開始',
        description: '強化されたMinecraftゲームプレイのためのLuminaV4使用クイックガイド。',
        mobileClient: {
          title: 'モバイルクライアントモード',
          description: 'Androidデバイスで直接LuminaV4を使用して、強化されたMinecraftゲームプレイを体験。',
          steps: [
            'LuminaV4を起動',
            '「モバイルクライアント」モードをクリック',
            '「アカウント」タブに移動し、Xbox Minecraftアカウントでログイン',
            '接続したいサーバーをタブサーバーセレクターから選択',
            '「開始」ボタンをクリック（Minecraftの読み込みを待つ）',
            'Minecraftサーバータブに移動し、Luminaサーバーがリストにあることを確認して接続'
          ]
        },
        remoteLink: {
          title: 'RemoteLinkモード',
          description: '詳細なRemoteLinkセットアップと使用手順については、専用のRemoteLinkシステムドキュメントを参照してください。',
          note: 'RemoteLinkを使用すると、Wi-Fi接続を使用して他のデバイスからLuminaV4をリモート制御できます。完全なセットアップ手順については、RemoteLinkシステムドキュメントを参照してください。'
        }
      },
      userInterface: {
        title: 'ユーザーインターフェース概要',
        description: 'LuminaV4は直感的なナビゲーションとカスタマイズ可能なレイアウトを備えたモダンなMaterial 3デザインを特徴としています。',
        components: [
          {
            name: 'メインダッシュボード',
            description: 'すべてのクライアント機能と設定にアクセスするための中央ハブ',
            features: [
              'クイックサーバー接続',
              '最近のサーバーリスト',
              '機能切り替えスイッチ',
              'パフォーマンスメトリクス表示'
            ]
          },
          {
            name: '設定パネル',
            description: 'すべてのクライアントオプションの包括的な設定インターフェース',
            categories: [
              '一般設定 - 基本クライアント設定',
              'パフォーマンス設定 - 最適化オプション',
              'ビジュアル設定 - UIカスタマイズとテーマ',
              'ネットワーク設定 - 接続とプロトコルオプション',
              'セキュリティ設定 - 認証とプライバシー制御'
            ]
          },
          {
            name: 'オーバーレイシステム',
            description: 'リアルタイム情報と制御を提供するゲーム内オーバーレイ',
            features: [
              'パフォーマンス監視（FPS、ping、メモリ使用量）',
              '機能ステータスインジケーター',
              'クイック設定アクセス',
              'デバッグ情報表示'
            ]
          }
        ]
      },
      coreFeatures: {
        title: 'コア機能',
        description: 'LuminaV4は、Minecraft Bedrock体験を向上させるために設計された包括的な機能セットを提供します。',
        categories: [
          {
            name: '接続管理',
            description: '高度なサーバー接続とプロトコル処理',
            features: [
              'マルチバージョンプロトコルサポート',
              '自動サーバー検出',
              '接続最適化',
              'レイテンシ削減技術'
            ],
            usage: `// サーバーに接続
ServerConnection connection = new ServerConnection();
connection.setAddress("play.example.com");
connection.setPort(19132);
connection.setProtocolVersion(ProtocolVersion.LATEST);
connection.connect();`
          },
          {
            name: 'パフォーマンス最適化',
            description: 'ゲームプレイ改善のためのシステムレベル最適化',
            features: [
              'メモリ管理最適化',
              'CPU使用量削減',
              'ネットワークパケット最適化',
              'レンダリングパフォーマンス改善'
            ]
          },
          {
            name: 'カスタマイズオプション',
            description: 'パーソナライズされた体験のための広範なカスタマイズ機能',
            features: [
              'カスタムUIテーマと色',
              '設定可能なキーバインド',
              '機能有効/無効切り替え',
              'パフォーマンスプロファイルプリセット'
            ]
          }
        ]
      },
      configuration: {
        title: '設定ガイド',
        description: '最適なパフォーマンスと機能のためのLuminaV4設定の詳細ガイド。',
        configFile: `{
  "client": {
    "version": "4.0.3",
    "autoUpdate": true,
    "theme": "dark"
  },
  "network": {
    "protocolVersion": "auto",
    "connectionTimeout": 5000,
    "maxRetries": 3,
    "compression": true
  },
  "performance": {
    "memoryOptimization": true,
    "cpuOptimization": true,
    "renderOptimization": true,
    "backgroundProcessing": false
  },
  "features": {
    "overlay": {
      "enabled": true,
      "position": "top-right",
      "opacity": 0.8
    },
    "monitoring": {
      "fps": true,
      "ping": true,
      "memory": true
    }
  },
  "security": {
    "encryptionEnabled": true,
    "authenticationRequired": true,
    "dataCollection": false
  }
}`,
        sections: [
          {
            name: 'クライアント設定',
            description: '基本クライアント設定と設定',
            options: [
              'version - クライアントバージョン情報',
              'autoUpdate - 自動更新チェック',
              'theme - UIテーマ選択（ダーク/ライト）'
            ]
          },
          {
            name: 'ネットワーク設定',
            description: 'ネットワークとプロトコル設定',
            options: [
              'protocolVersion - Minecraftプロトコルバージョン',
              'connectionTimeout - 接続タイムアウト（ミリ秒）',
              'maxRetries - 最大接続再試行回数',
              'compression - パケット圧縮を有効化'
            ]
          },
          {
            name: 'パフォーマンス設定',
            description: 'パフォーマンス最適化設定',
            options: [
              'memoryOptimization - メモリ最適化を有効化',
              'cpuOptimization - CPU最適化を有効化',
              'renderOptimization - レンダリング最適化を有効化',
              'backgroundProcessing - バックグラウンド処理を許可'
            ]
          }
        ]
      },
      troubleshooting: {
        title: 'トラブルシューティング',
        description: 'LuminaV4使用時の一般的な問題とその解決策。',
        issues: [
          {
            problem: '接続失敗',
            symptoms: ['サーバーに接続できない', 'タイムアウトエラー', 'プロトコル不一致警告'],
            solutions: [
              'サーバーアドレスとポートが正しいことを確認',
              'インターネット接続の安定性をチェック',
              'Minecraftバージョンの互換性を確認',
              '異なるプロトコルバージョン設定を試す',
              'アプリケーションを再起動して再試行'
            ]
          },
          {
            problem: 'パフォーマンス問題',
            symptoms: ['低FPS', '高メモリ使用量', 'アプリケーションラグ', '応答時間の遅延'],
            solutions: [
              '設定でパフォーマンス最適化を有効化',
              '不要なバックグラウンドアプリケーションを閉じる',
              'オーバーレイの複雑さと不透明度を下げる',
              'Minecraftのグラフィック設定を下げる',
              'メモリを解放するためにデバイスを再起動'
            ]
          },
          {
            problem: '認証エラー',
            symptoms: ['ログイン失敗', 'Microsoftアカウントエラー', 'トークン期限切れメッセージ'],
            solutions: [
              'Microsoftアカウントで再認証',
              'ログイン中のインターネット接続をチェック',
              'アプリケーションキャッシュとデータをクリア',
              'MicrosoftアカウントがXbox Liveアクセスを持つことを確認',
              'アプリケーションを最新バージョンに更新'
            ]
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.installation.title}</h2>
            <p>{t.installation.description}</p>
            
            <h3>{language === 'en' ? 'System Requirements' : 'システム要件'}</h3>
            <ul>
              {t.installation.requirements.map((req, index) => (
                <li key={index}>{req}</li>
              ))}
            </ul>
            
            <h3>{language === 'en' ? 'Installation Steps' : 'インストール手順'}</h3>
            <ol>
              {t.installation.steps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
          </section>

          <section className="docs-section">
            <h2>{t.usage.title}</h2>
            <p>{t.usage.description}</p>

            <div className="feature-grid">
              <div className="feature-item">
                <h3>{t.usage.mobileClient.title}</h3>
                <p>{t.usage.mobileClient.description}</p>
                <ol>
                  {t.usage.mobileClient.steps.map((step, index) => (
                    <li key={index}>{step}</li>
                  ))}
                </ol>
              </div>

              <div className="feature-item">
                <h3>{t.usage.remoteLink.title}</h3>
                <p>{t.usage.remoteLink.description}</p>
                {'note' in t.usage.remoteLink && (
                  <div className="docs-callout info">
                    <p>{t.usage.remoteLink.note}</p>
                    <p>
                      <a href="#/docs/remotelink-system" className="docs-link">
                        {language === 'en' ? '→ View RemoteLink System Documentation' : '→ RemoteLinkシステムドキュメントを表示'}
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.userInterface.title}</h2>
            <p>{t.userInterface.description}</p>
            
            <div className="module-list">
              {t.userInterface.components.map((component, index) => (
                <div key={index} className="module-item">
                  <h4>{component.name}</h4>
                  <p>{component.description}</p>
                  {component.features && (
                    <ul>
                      {component.features.map((feature, featureIndex) => (
                        <li key={featureIndex}>{feature}</li>
                      ))}
                    </ul>
                  )}
                  {component.categories && (
                    <ul>
                      {component.categories.map((category, categoryIndex) => (
                        <li key={categoryIndex}>{category}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.coreFeatures.title}</h2>
            <p>{t.coreFeatures.description}</p>
            
            {t.coreFeatures.categories.map((category, index) => (
              <div key={index}>
                <h3>{category.name}</h3>
                <p>{category.description}</p>
                <ul>
                  {category.features.map((feature, featureIndex) => (
                    <li key={featureIndex}>{feature}</li>
                  ))}
                </ul>
                {category.usage && (
                  <CodeBlock 
                    code={category.usage}
                    language="java"
                    title={category.name}
                  />
                )}
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.configuration.title}</h2>
            <p>{t.configuration.description}</p>
            
            <CodeBlock 
              code={t.configuration.configFile}
              language="json"
              title="lumina-config.json"
            />
            
            {t.configuration.sections.map((section, index) => (
              <div key={index}>
                <h3>{section.name}</h3>
                <p>{section.description}</p>
                <ul>
                  {section.options.map((option, optionIndex) => (
                    <li key={optionIndex}>{option}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.troubleshooting.title}</h2>
            <p>{t.troubleshooting.description}</p>
            
            {t.troubleshooting.issues.map((issue, index) => (
              <div key={index} className="docs-callout">
                <h4>{issue.problem}</h4>
                <h5>{language === 'en' ? 'Symptoms:' : '症状:'}</h5>
                <ul>
                  {issue.symptoms.map((symptom, symptomIndex) => (
                    <li key={symptomIndex}>{symptom}</li>
                  ))}
                </ul>
                <h5>{language === 'en' ? 'Solutions:' : '解決策:'}</h5>
                <ul>
                  {issue.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex}>{solution}</li>
                  ))}
                </ul>
              </div>
            ))}
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsApplicationUsagePage;
