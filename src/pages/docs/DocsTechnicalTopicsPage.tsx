import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsTechnicalTopicsPage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'Technical Topics',
      subtitle: 'Development setup, build process, and advanced technical details',
      developmentSetup: {
        title: 'Development Setup',
        description: 'Complete guide to setting up a development environment for LuminaV4.',
        requirements: [
          'Android Studio Arctic Fox (2020.3.1) or later',
          'JDK 17 or higher',
          'Android SDK API level 28 or higher',
          'NDK 21.4.7075529 or later for native components',
          'Git for version control',
          'At least 8GB RAM and 20GB free disk space'
        ],
        setupSteps: `# 1. Clone the repository
git clone https://github.com/TheProjectLumina/LuminaClient.git
cd LuminaClient

# 2. Setup Firebase configuration
# Download google-services.json from Firebase Console
# Place it in app/ directory

# 3. Configure local.properties
echo "sdk.dir=/path/to/android/sdk" > local.properties
echo "ndk.dir=/path/to/android/ndk" >> local.properties

# 4. Build the project
./gradlew assembleDebug

# 5. Install on device
./gradlew installDebug`,
        firebaseSetup: `// Firebase configuration steps:
// 1. Create a new Firebase project
// 2. Add Android app with package name: com.project.lumina.client
// 3. Download google-services.json
// 4. Enable Analytics and Crashlytics
// 5. Configure authentication providers if needed

// Example Firebase initialization
class LuminaApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        FirebaseApp.initializeApp(this)
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)
    }
}`
      },
      buildProcess: {
        title: 'Build Process',
        description: 'Understanding the LuminaV4 build system and compilation process.',
        gradleConfiguration: `// Multi-module build configuration
// Root build.gradle.kts
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
    id("com.google.gms.google-services") version "4.4.2" apply false
    id("com.google.firebase.crashlytics") version "3.0.3" apply false
}

// App module build.gradle.kts
android {
    compileSdk = 35

    defaultConfig {
        minSdk = 28
        targetSdk = 35
        versionCode = 2
        versionName = "4.0.3"

        ndk {
            abiFilters += setOf("arm64-v8a", "armeabi-v7a")
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}`,
        nativeBuild: `# CMakeLists.txt for native components
cmake_minimum_required(VERSION 3.22.1)
project("lumina-native")

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_library(log-lib log)
find_library(android-lib android)

# Add source files
add_library(lumina-native SHARED
    jni_bridge.cpp
    hsv_to_rgb.cpp
    performance_monitor.cpp
    memory_manager.cpp
)

# Link libraries
target_link_libraries(lumina-native
    \${log-lib}
    \${android-lib}
    EGL
    GLESv3
)

# Compiler flags
target_compile_options(lumina-native PRIVATE
    -Wall
    -Wextra
    -O3
    -ffast-math
)`
      },
      performanceOptimization: {
        title: 'Performance Optimization',
        description: 'Techniques and strategies used in LuminaV4 for optimal performance.',
        memoryManagement: `// Memory optimization strategies
class MemoryManager {
    companion object {
        private const val MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB
        private const val GC_THRESHOLD = 0.8f
    }

    private val memoryCache = LruCache<String, Any>(MAX_CACHE_SIZE)
    private val memoryMonitor = MemoryMonitor()

    fun optimizeMemory() {
        val memoryInfo = getMemoryInfo()
        val usageRatio = memoryInfo.used.toFloat() / memoryInfo.total

        if (usageRatio > GC_THRESHOLD) {
            // Trigger aggressive cleanup
            clearCaches()
            System.gc()

            // Reduce cache sizes temporarily
            memoryCache.resize((MAX_CACHE_SIZE * 0.5).toInt())
        }
    }

    private fun clearCaches() {
        memoryCache.evictAll()
        ImageCache.getInstance().clear()
        NetworkCache.getInstance().clear()
    }
}`,
        networkOptimization: `// Network performance optimization
class NetworkOptimizer {
    private val packetQueue = PriorityQueue<Packet>(compareBy { it.priority })
    private val compressionEnabled = true
    private val batchingEnabled = true

    fun optimizePacketSending(packets: List<Packet>) {
        if (batchingEnabled && packets.size > 1) {
            val batchedPacket = batchPackets(packets)
            sendPacket(batchedPacket)
        } else {
            packets.forEach { packet ->
                if (compressionEnabled && packet.size > COMPRESSION_THRESHOLD) {
                    packet.compress()
                }
                sendPacket(packet)
            }
        }
    }

    private fun batchPackets(packets: List<Packet>): BatchPacket {
        return BatchPacket().apply {
            addAll(packets)
            if (compressionEnabled) compress()
        }
    }
}`
      },
      securityImplementation: {
        title: 'Security Implementation',
        description: 'Security measures and cryptographic implementations in LuminaV4.',
        encryption: `// Encryption and security utilities
class SecurityManager {
    private val keyGenerator = KeyGenerator.getInstance("AES")
    private val cipher = Cipher.getInstance("AES/GCM/NoPadding")

    fun encryptData(data: ByteArray, key: SecretKey): EncryptedData {
        cipher.init(Cipher.ENCRYPT_MODE, key)
        val iv = cipher.iv
        val encryptedBytes = cipher.doFinal(data)

        return EncryptedData(
            data = encryptedBytes,
            iv = iv,
            algorithm = "AES/GCM/NoPadding"
        )
    }

    fun decryptData(encryptedData: EncryptedData, key: SecretKey): ByteArray {
        val spec = GCMParameterSpec(128, encryptedData.iv)
        cipher.init(Cipher.DECRYPT_MODE, key, spec)
        return cipher.doFinal(encryptedData.data)
    }

    fun validateSignature(data: ByteArray, signature: ByteArray, publicKey: PublicKey): Boolean {
        val verifier = Signature.getInstance("SHA256withRSA")
        verifier.initVerify(publicKey)
        verifier.update(data)
        return verifier.verify(signature)
    }
}`,
        authentication: `// Microsoft authentication implementation
class MicrosoftAuthManager {
    private val authClient = MicrosoftAuthClient()
    private val tokenStorage = SecureTokenStorage()

    suspend fun authenticateUser(): AuthResult {
        return try {
            // Step 1: Get device code
            val deviceCode = authClient.getDeviceCode()

            // Step 2: Show user code to user
            showUserCode(deviceCode.userCode)

            // Step 3: Poll for token
            val tokens = pollForTokens(deviceCode)

            // Step 4: Validate and store tokens
            val userProfile = validateTokens(tokens)
            tokenStorage.storeTokens(tokens)

            AuthResult.Success(userProfile)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "Authentication failed")
        }
    }

    private suspend fun pollForTokens(deviceCode: DeviceCode): Tokens {
        val pollInterval = deviceCode.interval * 1000L
        val expiresAt = System.currentTimeMillis() + (deviceCode.expiresIn * 1000L)

        while (System.currentTimeMillis() < expiresAt) {
            delay(pollInterval)

            try {
                return authClient.getTokens(deviceCode.deviceCode)
            } catch (e: PendingAuthException) {
                // Continue polling
                continue
            }
        }

        throw TimeoutException("Authentication timed out")
    }
}`
      }
    },
    ja: {
      title: '技術的トピック',
      subtitle: '開発セットアップ、ビルドプロセス、高度な技術詳細',
      developmentSetup: {
        title: '開発セットアップ',
        description: 'LuminaV4の開発環境セットアップの完全ガイド。',
        requirements: [
          'Android Studio Arctic Fox (2020.3.1) 以降',
          'JDK 17 以上',
          'Android SDK APIレベル28以上',
          'ネイティブコンポーネント用NDK 21.4.7075529以降',
          'バージョン管理用Git',
          '最低8GB RAMと20GB空きディスク容量'
        ],
        setupSteps: `# 1. リポジトリをクローン
git clone https://github.com/TheProjectLumina/LuminaClient.git
cd LuminaClient

# 2. Firebase設定をセットアップ
# Firebase Consoleからgoogle-services.jsonをダウンロード
# app/ディレクトリに配置

# 3. local.propertiesを設定
echo "sdk.dir=/path/to/android/sdk" > local.properties
echo "ndk.dir=/path/to/android/ndk" >> local.properties

# 4. プロジェクトをビルド
./gradlew assembleDebug

# 5. デバイスにインストール
./gradlew installDebug`,
        firebaseSetup: `// Firebase設定手順:
// 1. 新しいFirebaseプロジェクトを作成
// 2. パッケージ名でAndroidアプリを追加: com.project.lumina.client
// 3. google-services.jsonをダウンロード
// 4. AnalyticsとCrashlyticsを有効化
// 5. 必要に応じて認証プロバイダーを設定

// Firebase初期化例
class LuminaApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        FirebaseApp.initializeApp(this)
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)
    }
}`
      },
      buildProcess: {
        title: 'ビルドプロセス',
        description: 'LuminaV4ビルドシステムとコンパイルプロセスの理解。',
        gradleConfiguration: `// マルチモジュールビルド設定
// ルート build.gradle.kts
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
    id("com.google.gms.google-services") version "4.4.2" apply false
    id("com.google.firebase.crashlytics") version "3.0.3" apply false
}

// アプリモジュール build.gradle.kts
android {
    compileSdk = 35

    defaultConfig {
        minSdk = 28
        targetSdk = 35
        versionCode = 2
        versionName = "4.0.3"

        ndk {
            abiFilters += setOf("arm64-v8a", "armeabi-v7a")
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}`,
        nativeBuild: `# ネイティブコンポーネント用CMakeLists.txt
cmake_minimum_required(VERSION 3.22.1)
project("lumina-native")

# C++標準を設定
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 必要なパッケージを検索
find_library(log-lib log)
find_library(android-lib android)

# ソースファイルを追加
add_library(lumina-native SHARED
    jni_bridge.cpp
    hsv_to_rgb.cpp
    performance_monitor.cpp
    memory_manager.cpp
)

# ライブラリをリンク
target_link_libraries(lumina-native
    \${log-lib}
    \${android-lib}
    EGL
    GLESv3
)

# コンパイラフラグ
target_compile_options(lumina-native PRIVATE
    -Wall
    -Wextra
    -O3
    -ffast-math
)`
      },
      performanceOptimization: {
        title: 'パフォーマンス最適化',
        description: 'LuminaV4で最適なパフォーマンスのために使用される技術と戦略。',
        memoryManagement: `// メモリ最適化戦略
class MemoryManager {
    companion object {
        private const val MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB
        private const val GC_THRESHOLD = 0.8f
    }

    private val memoryCache = LruCache<String, Any>(MAX_CACHE_SIZE)
    private val memoryMonitor = MemoryMonitor()

    fun optimizeMemory() {
        val memoryInfo = getMemoryInfo()
        val usageRatio = memoryInfo.used.toFloat() / memoryInfo.total

        if (usageRatio > GC_THRESHOLD) {
            // アグレッシブなクリーンアップをトリガー
            clearCaches()
            System.gc()

            // キャッシュサイズを一時的に削減
            memoryCache.resize((MAX_CACHE_SIZE * 0.5).toInt())
        }
    }

    private fun clearCaches() {
        memoryCache.evictAll()
        ImageCache.getInstance().clear()
        NetworkCache.getInstance().clear()
    }
}`,
        networkOptimization: `// ネットワークパフォーマンス最適化
class NetworkOptimizer {
    private val packetQueue = PriorityQueue<Packet>(compareBy { it.priority })
    private val compressionEnabled = true
    private val batchingEnabled = true

    fun optimizePacketSending(packets: List<Packet>) {
        if (batchingEnabled && packets.size > 1) {
            val batchedPacket = batchPackets(packets)
            sendPacket(batchedPacket)
        } else {
            packets.forEach { packet ->
                if (compressionEnabled && packet.size > COMPRESSION_THRESHOLD) {
                    packet.compress()
                }
                sendPacket(packet)
            }
        }
    }

    private fun batchPackets(packets: List<Packet>): BatchPacket {
        return BatchPacket().apply {
            addAll(packets)
            if (compressionEnabled) compress()
        }
    }
}`
      },
      securityImplementation: {
        title: 'セキュリティ実装',
        description: 'LuminaV4のセキュリティ対策と暗号化実装。',
        encryption: `// 暗号化とセキュリティユーティリティ
class SecurityManager {
    private val keyGenerator = KeyGenerator.getInstance("AES")
    private val cipher = Cipher.getInstance("AES/GCM/NoPadding")

    fun encryptData(data: ByteArray, key: SecretKey): EncryptedData {
        cipher.init(Cipher.ENCRYPT_MODE, key)
        val iv = cipher.iv
        val encryptedBytes = cipher.doFinal(data)

        return EncryptedData(
            data = encryptedBytes,
            iv = iv,
            algorithm = "AES/GCM/NoPadding"
        )
    }

    fun decryptData(encryptedData: EncryptedData, key: SecretKey): ByteArray {
        val spec = GCMParameterSpec(128, encryptedData.iv)
        cipher.init(Cipher.DECRYPT_MODE, key, spec)
        return cipher.doFinal(encryptedData.data)
    }

    fun validateSignature(data: ByteArray, signature: ByteArray, publicKey: PublicKey): Boolean {
        val verifier = Signature.getInstance("SHA256withRSA")
        verifier.initVerify(publicKey)
        verifier.update(data)
        return verifier.verify(signature)
    }
}`,
        authentication: `// Microsoft認証実装
class MicrosoftAuthManager {
    private val authClient = MicrosoftAuthClient()
    private val tokenStorage = SecureTokenStorage()

    suspend fun authenticateUser(): AuthResult {
        return try {
            // ステップ1: デバイスコードを取得
            val deviceCode = authClient.getDeviceCode()

            // ステップ2: ユーザーコードをユーザーに表示
            showUserCode(deviceCode.userCode)

            // ステップ3: トークンをポーリング
            val tokens = pollForTokens(deviceCode)

            // ステップ4: トークンを検証して保存
            val userProfile = validateTokens(tokens)
            tokenStorage.storeTokens(tokens)

            AuthResult.Success(userProfile)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "認証に失敗しました")
        }
    }

    private suspend fun pollForTokens(deviceCode: DeviceCode): Tokens {
        val pollInterval = deviceCode.interval * 1000L
        val expiresAt = System.currentTimeMillis() + (deviceCode.expiresIn * 1000L)

        while (System.currentTimeMillis() < expiresAt) {
            delay(pollInterval)

            try {
                return authClient.getTokens(deviceCode.deviceCode)
            } catch (e: PendingAuthException) {
                // ポーリングを続行
                continue
            }
        }

        throw TimeoutException("認証がタイムアウトしました")
    }
}`
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.developmentSetup.title}</h2>
            <p>{t.developmentSetup.description}</p>

            <h3>{language === 'en' ? 'Requirements' : '要件'}</h3>
            <ul>
              {t.developmentSetup.requirements.map((req, index) => (
                <li key={index}>{req}</li>
              ))}
            </ul>

            <h3>{language === 'en' ? 'Setup Steps' : 'セットアップ手順'}</h3>
            <CodeBlock
              code={t.developmentSetup.setupSteps}
              language="bash"
              title="Development Setup"
            />

            <h3>{language === 'en' ? 'Firebase Configuration' : 'Firebase設定'}</h3>
            <CodeBlock
              code={t.developmentSetup.firebaseSetup}
              language="kotlin"
              title="Firebase Setup"
            />
          </section>

          <section className="docs-section">
            <h2>{t.buildProcess.title}</h2>
            <p>{t.buildProcess.description}</p>

            <h3>{language === 'en' ? 'Gradle Configuration' : 'Gradle設定'}</h3>
            <CodeBlock
              code={t.buildProcess.gradleConfiguration}
              language="gradle"
              title="build.gradle.kts"
            />

            <h3>{language === 'en' ? 'Native Build Configuration' : 'ネイティブビルド設定'}</h3>
            <CodeBlock
              code={t.buildProcess.nativeBuild}
              language="cmake"
              title="CMakeLists.txt"
            />
          </section>

          <section className="docs-section">
            <h2>{t.performanceOptimization.title}</h2>
            <p>{t.performanceOptimization.description}</p>

            <h3>{language === 'en' ? 'Memory Management' : 'メモリ管理'}</h3>
            <CodeBlock
              code={t.performanceOptimization.memoryManagement}
              language="kotlin"
              title="MemoryManager.kt"
            />

            <h3>{language === 'en' ? 'Network Optimization' : 'ネットワーク最適化'}</h3>
            <CodeBlock
              code={t.performanceOptimization.networkOptimization}
              language="kotlin"
              title="NetworkOptimizer.kt"
            />
          </section>

          <section className="docs-section">
            <h2>{t.securityImplementation.title}</h2>
            <p>{t.securityImplementation.description}</p>

            <h3>{language === 'en' ? 'Encryption Implementation' : '暗号化実装'}</h3>
            <CodeBlock
              code={t.securityImplementation.encryption}
              language="kotlin"
              title="SecurityManager.kt"
            />

            <h3>{language === 'en' ? 'Authentication System' : '認証システム'}</h3>
            <CodeBlock
              code={t.securityImplementation.authentication}
              language="kotlin"
              title="MicrosoftAuthManager.kt"
            />
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsTechnicalTopicsPage;