import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsIntegrationGuidesPage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'Integration Guides',
      subtitle: 'How various components integrate within the LuminaV4 application',
      protocolIntegration: {
        title: 'Protocol Integration',
        description: 'Understanding how the Protocol module integrates with the main application for Minecraft communication.',
        architecture: `Application Layer
├── MainActivity (UI)
├── GameService (Background)
└── ConnectionManager
    ├── Protocol Module
    │   ├── BedrockCodec
    │   ├── PacketHandler
    │   └── VersionManager
    ├── Network Module
    │   ├── RakNetTransport
    │   ├── QueryCodec
    │   └── RCONCodec
    └── Authentication
        └── MicrosoftAuth`,
        implementation: `// Protocol integration in ConnectionManager
public class ConnectionManager {
    private BedrockCodec codec;
    private RakNetTransport transport;
    private PacketHandler packetHandler;
    
    public void initializeConnection(ServerInfo server) {
        // Initialize protocol codec
        codec = BedrockCodec.builder()
            .protocolVersion(server.getProtocolVersion())
            .build();
            
        // Setup transport layer
        transport = new RakNetTransport();
        transport.setCodec(codec);
        
        // Configure packet handling
        packetHandler = new PacketHandler();
        packetHandler.registerHandlers(getDefaultHandlers());
        
        // Establish connection
        transport.connect(server.getAddress(), server.getPort());
    }
    
    private Map<Class<?>, PacketHandler> getDefaultHandlers() {
        Map<Class<?>, PacketHandler> handlers = new HashMap<>();
        handlers.put(LoginPacket.class, this::handleLogin);
        handlers.put(PlayStatusPacket.class, this::handlePlayStatus);
        handlers.put(DisconnectPacket.class, this::handleDisconnect);
        return handlers;
    }
}`
      },
      uiIntegration: {
        title: 'UI Integration',
        description: 'How Jetpack Compose UI components integrate with the underlying client functionality.',
        composableStructure: `@Composable
fun LuminaMainScreen(
    viewModel: LuminaViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LuminaTheme {
        Scaffold(
            topBar = { LuminaTopBar() },
            bottomBar = { LuminaBottomBar() }
        ) { paddingValues ->
            when (uiState.currentScreen) {
                Screen.Dashboard -> DashboardScreen(
                    modifier = Modifier.padding(paddingValues),
                    onServerConnect = viewModel::connectToServer,
                    onSettingsClick = viewModel::openSettings
                )
                Screen.Settings -> SettingsScreen(
                    modifier = Modifier.padding(paddingValues),
                    settings = uiState.settings,
                    onSettingChanged = viewModel::updateSetting
                )
                Screen.ServerList -> ServerListScreen(
                    modifier = Modifier.padding(paddingValues),
                    servers = uiState.servers,
                    onServerSelected = viewModel::selectServer
                )
            }
        }
    }
}`,
        viewModelIntegration: `@HiltViewModel
class LuminaViewModel @Inject constructor(
    private val connectionManager: ConnectionManager,
    private val settingsRepository: SettingsRepository,
    private val serverRepository: ServerRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(LuminaUiState())
    val uiState: StateFlow<LuminaUiState> = _uiState.asStateFlow()
    
    fun connectToServer(server: ServerInfo) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isConnecting = true) }
                connectionManager.connect(server)
                _uiState.update { 
                    it.copy(
                        isConnecting = false,
                        isConnected = true,
                        currentServer = server
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isConnecting = false,
                        error = e.message
                    )
                }
            }
        }
    }
}`
      },
      serviceIntegration: {
        title: 'Service Integration',
        description: 'How background services integrate with the main application for continuous operation.',
        serviceImplementation: `@AndroidEntryPoint
class LuminaService : Service() {
    
    @Inject
    lateinit var connectionManager: ConnectionManager
    
    @Inject
    lateinit var overlayManager: OverlayManager
    
    private val binder = LuminaServiceBinder()
    
    override fun onBind(intent: Intent): IBinder = binder
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> startCapture()
            ACTION_STOP_CAPTURE -> stopCapture()
            ACTION_UPDATE_OVERLAY -> updateOverlay()
        }
        return START_STICKY
    }
    
    private fun startCapture() {
        createNotificationChannel()
        val notification = createForegroundNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        overlayManager.showOverlay()
        connectionManager.startMonitoring()
    }
    
    inner class LuminaServiceBinder : Binder() {
        fun getService(): LuminaService = this@LuminaService
    }
}`,
        manifestIntegration: `<service
    android:name="com.project.lumina.client.service.LuminaService"
    android:exported="false"
    android:foregroundServiceType="specialUse">
    
    <property
        android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
        android:value="Lumina Capture" />
    
    <intent-filter>
        <action android:name="com.project.lumina.relay.capture.start" />
        <action android:name="com.project.lumina.relay.capture.stop" />
        <category android:name="android.intent.category.DEFAULT" />
    </intent-filter>
</service>`
      },
      nativeIntegration: {
        title: 'Native Integration',
        description: 'How C++ native components integrate with the Java/Kotlin application layer.',
        jniInterface: `// JNI bridge implementation
public class NativeBridge {
    static {
        System.loadLibrary("lumina-native");
    }
    
    // Native method declarations
    public static native void initializeNative();
    public static native int[] hsvToRgb(float h, float s, float v);
    public static native void processPacketData(byte[] data, int length);
    public static native boolean validateSignature(byte[] data, byte[] signature);
    
    // Callback methods called from native code
    public static void onNativeEvent(int eventType, String data) {
        EventBus.getDefault().post(new NativeEvent(eventType, data));
    }
    
    public static void onPerformanceUpdate(float fps, long memoryUsage) {
        PerformanceMonitor.getInstance().updateMetrics(fps, memoryUsage);
    }
}`,
        cppImplementation: `// C++ implementation (jni_bridge.cpp)
#include <jni.h>
#include "hsv_to_rgb.h"

extern "C" {
    JNIEXPORT void JNICALL
    Java_com_project_lumina_client_NativeBridge_initializeNative(
        JNIEnv *env, jclass clazz) {
        // Initialize native components
        initialize_performance_monitor();
        setup_memory_management();
    }
    
    JNIEXPORT jintArray JNICALL
    Java_com_project_lumina_client_NativeBridge_hsvToRgb(
        JNIEnv *env, jclass clazz, jfloat h, jfloat s, jfloat v) {
        
        int rgb[3];
        hsv_to_rgb(h, s, v, rgb);
        
        jintArray result = env->NewIntArray(3);
        env->SetIntArrayRegion(result, 0, 3, rgb);
        return result;
    }
}`
      },
      dataFlow: {
        title: 'Data Flow Integration',
        description: 'Understanding how data flows between different components of the application.',
        flowDiagram: `User Input → UI Layer → ViewModel → Repository → Service Layer
    ↓                ↓           ↓            ↓            ↓
Android UI → Compose UI → State Mgmt → Data Layer → Network/Protocol
    ↓                ↓           ↓            ↓            ↓
Activities → Fragments → LiveData → Room/Prefs → Minecraft Server`,
        implementation: `// Data flow example: Server connection
class ServerConnectionFlow {
    
    // 1. User initiates connection from UI
    @Composable
    fun ConnectButton(onConnect: (ServerInfo) -> Unit) {
        Button(onClick = { onConnect(selectedServer) }) {
            Text("Connect")
        }
    }
    
    // 2. ViewModel processes the request
    fun connectToServer(server: ServerInfo) {
        viewModelScope.launch {
            repository.connectToServer(server)
                .flowOn(Dispatchers.IO)
                .catch { error -> handleError(error) }
                .collect { status -> updateConnectionStatus(status) }
        }
    }
    
    // 3. Repository coordinates with services
    suspend fun connectToServer(server: ServerInfo): Flow<ConnectionStatus> {
        return flow {
            emit(ConnectionStatus.Connecting)
            
            val result = connectionService.establishConnection(server)
            if (result.isSuccess) {
                emit(ConnectionStatus.Connected)
                startMonitoring(server)
            } else {
                emit(ConnectionStatus.Failed(result.error))
            }
        }
    }
    
    // 4. Service handles low-level operations
    suspend fun establishConnection(server: ServerInfo): ConnectionResult {
        return withContext(Dispatchers.IO) {
            protocolManager.initializeProtocol(server.protocolVersion)
            networkManager.connect(server.address, server.port)
        }
    }
}`
      }
    },
    ja: {
      title: '統合ガイド',
      subtitle: 'LuminaV4アプリケーション内での様々なコンポーネントの統合方法',
      protocolIntegration: {
        title: 'プロトコル統合',
        description: 'Minecraft通信のためにプロトコルモジュールがメインアプリケーションとどのように統合されるかを理解する。',
        architecture: `アプリケーション層
├── MainActivity (UI)
├── GameService (バックグラウンド)
└── ConnectionManager
    ├── プロトコルモジュール
    │   ├── BedrockCodec
    │   ├── PacketHandler
    │   └── VersionManager
    ├── ネットワークモジュール
    │   ├── RakNetTransport
    │   ├── QueryCodec
    │   └── RCONCodec
    └── 認証
        └── MicrosoftAuth`,
        implementation: `// ConnectionManagerでのプロトコル統合
public class ConnectionManager {
    private BedrockCodec codec;
    private RakNetTransport transport;
    private PacketHandler packetHandler;
    
    public void initializeConnection(ServerInfo server) {
        // プロトコルコーデックを初期化
        codec = BedrockCodec.builder()
            .protocolVersion(server.getProtocolVersion())
            .build();
            
        // トランスポート層をセットアップ
        transport = new RakNetTransport();
        transport.setCodec(codec);
        
        // パケット処理を設定
        packetHandler = new PacketHandler();
        packetHandler.registerHandlers(getDefaultHandlers());
        
        // 接続を確立
        transport.connect(server.getAddress(), server.getPort());
    }
    
    private Map<Class<?>, PacketHandler> getDefaultHandlers() {
        Map<Class<?>, PacketHandler> handlers = new HashMap<>();
        handlers.put(LoginPacket.class, this::handleLogin);
        handlers.put(PlayStatusPacket.class, this::handlePlayStatus);
        handlers.put(DisconnectPacket.class, this::handleDisconnect);
        return handlers;
    }
}`
      },
      uiIntegration: {
        title: 'UI統合',
        description: 'Jetpack Compose UIコンポーネントが基盤となるクライアント機能とどのように統合されるか。',
        composableStructure: `@Composable
fun LuminaMainScreen(
    viewModel: LuminaViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LuminaTheme {
        Scaffold(
            topBar = { LuminaTopBar() },
            bottomBar = { LuminaBottomBar() }
        ) { paddingValues ->
            when (uiState.currentScreen) {
                Screen.Dashboard -> DashboardScreen(
                    modifier = Modifier.padding(paddingValues),
                    onServerConnect = viewModel::connectToServer,
                    onSettingsClick = viewModel::openSettings
                )
                Screen.Settings -> SettingsScreen(
                    modifier = Modifier.padding(paddingValues),
                    settings = uiState.settings,
                    onSettingChanged = viewModel::updateSetting
                )
                Screen.ServerList -> ServerListScreen(
                    modifier = Modifier.padding(paddingValues),
                    servers = uiState.servers,
                    onServerSelected = viewModel::selectServer
                )
            }
        }
    }
}`,
        viewModelIntegration: `@HiltViewModel
class LuminaViewModel @Inject constructor(
    private val connectionManager: ConnectionManager,
    private val settingsRepository: SettingsRepository,
    private val serverRepository: ServerRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(LuminaUiState())
    val uiState: StateFlow<LuminaUiState> = _uiState.asStateFlow()
    
    fun connectToServer(server: ServerInfo) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isConnecting = true) }
                connectionManager.connect(server)
                _uiState.update { 
                    it.copy(
                        isConnecting = false,
                        isConnected = true,
                        currentServer = server
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isConnecting = false,
                        error = e.message
                    )
                }
            }
        }
    }
}`
      },
      serviceIntegration: {
        title: 'サービス統合',
        description: '継続的な動作のためにバックグラウンドサービスがメインアプリケーションとどのように統合されるか。',
        serviceImplementation: `@AndroidEntryPoint
class LuminaService : Service() {
    
    @Inject
    lateinit var connectionManager: ConnectionManager
    
    @Inject
    lateinit var overlayManager: OverlayManager
    
    private val binder = LuminaServiceBinder()
    
    override fun onBind(intent: Intent): IBinder = binder
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> startCapture()
            ACTION_STOP_CAPTURE -> stopCapture()
            ACTION_UPDATE_OVERLAY -> updateOverlay()
        }
        return START_STICKY
    }
    
    private fun startCapture() {
        createNotificationChannel()
        val notification = createForegroundNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        overlayManager.showOverlay()
        connectionManager.startMonitoring()
    }
    
    inner class LuminaServiceBinder : Binder() {
        fun getService(): LuminaService = this@LuminaService
    }
}`,
        manifestIntegration: `<service
    android:name="com.project.lumina.client.service.LuminaService"
    android:exported="false"
    android:foregroundServiceType="specialUse">
    
    <property
        android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
        android:value="Lumina Capture" />
    
    <intent-filter>
        <action android:name="com.project.lumina.relay.capture.start" />
        <action android:name="com.project.lumina.relay.capture.stop" />
        <category android:name="android.intent.category.DEFAULT" />
    </intent-filter>
</service>`
      },
      nativeIntegration: {
        title: 'ネイティブ統合',
        description: 'C++ネイティブコンポーネントがJava/Kotlinアプリケーション層とどのように統合されるか。',
        jniInterface: `// JNIブリッジ実装
public class NativeBridge {
    static {
        System.loadLibrary("lumina-native");
    }
    
    // ネイティブメソッド宣言
    public static native void initializeNative();
    public static native int[] hsvToRgb(float h, float s, float v);
    public static native void processPacketData(byte[] data, int length);
    public static native boolean validateSignature(byte[] data, byte[] signature);
    
    // ネイティブコードから呼び出されるコールバックメソッド
    public static void onNativeEvent(int eventType, String data) {
        EventBus.getDefault().post(new NativeEvent(eventType, data));
    }
    
    public static void onPerformanceUpdate(float fps, long memoryUsage) {
        PerformanceMonitor.getInstance().updateMetrics(fps, memoryUsage);
    }
}`,
        cppImplementation: `// C++実装 (jni_bridge.cpp)
#include <jni.h>
#include "hsv_to_rgb.h"

extern "C" {
    JNIEXPORT void JNICALL
    Java_com_project_lumina_client_NativeBridge_initializeNative(
        JNIEnv *env, jclass clazz) {
        // ネイティブコンポーネントを初期化
        initialize_performance_monitor();
        setup_memory_management();
    }
    
    JNIEXPORT jintArray JNICALL
    Java_com_project_lumina_client_NativeBridge_hsvToRgb(
        JNIEnv *env, jclass clazz, jfloat h, jfloat s, jfloat v) {
        
        int rgb[3];
        hsv_to_rgb(h, s, v, rgb);
        
        jintArray result = env->NewIntArray(3);
        env->SetIntArrayRegion(result, 0, 3, rgb);
        return result;
    }
}`
      },
      dataFlow: {
        title: 'データフロー統合',
        description: 'アプリケーションの異なるコンポーネント間でデータがどのように流れるかを理解する。',
        flowDiagram: `ユーザー入力 → UI層 → ViewModel → Repository → サービス層
    ↓           ↓        ↓           ↓            ↓
Android UI → Compose UI → 状態管理 → データ層 → ネットワーク/プロトコル
    ↓           ↓        ↓           ↓            ↓
アクティビティ → フラグメント → LiveData → Room/Prefs → Minecraftサーバー`,
        implementation: `// データフロー例：サーバー接続
class ServerConnectionFlow {
    
    // 1. ユーザーがUIから接続を開始
    @Composable
    fun ConnectButton(onConnect: (ServerInfo) -> Unit) {
        Button(onClick = { onConnect(selectedServer) }) {
            Text("接続")
        }
    }
    
    // 2. ViewModelがリクエストを処理
    fun connectToServer(server: ServerInfo) {
        viewModelScope.launch {
            repository.connectToServer(server)
                .flowOn(Dispatchers.IO)
                .catch { error -> handleError(error) }
                .collect { status -> updateConnectionStatus(status) }
        }
    }
    
    // 3. Repositoryがサービスと調整
    suspend fun connectToServer(server: ServerInfo): Flow<ConnectionStatus> {
        return flow {
            emit(ConnectionStatus.Connecting)
            
            val result = connectionService.establishConnection(server)
            if (result.isSuccess) {
                emit(ConnectionStatus.Connected)
                startMonitoring(server)
            } else {
                emit(ConnectionStatus.Failed(result.error))
            }
        }
    }
    
    // 4. サービスが低レベル操作を処理
    suspend fun establishConnection(server: ServerInfo): ConnectionResult {
        return withContext(Dispatchers.IO) {
            protocolManager.initializeProtocol(server.protocolVersion)
            networkManager.connect(server.address, server.port)
        }
    }
}`
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.protocolIntegration.title}</h2>
            <p>{t.protocolIntegration.description}</p>
            
            <h3>{language === 'en' ? 'Architecture Overview' : 'アーキテクチャ概要'}</h3>
            <CodeBlock 
              code={t.protocolIntegration.architecture}
              language="text"
              title="Integration Architecture"
            />
            
            <h3>{language === 'en' ? 'Implementation Example' : '実装例'}</h3>
            <CodeBlock 
              code={t.protocolIntegration.implementation}
              language="java"
              title="ConnectionManager.java"
            />
          </section>

          <section className="docs-section">
            <h2>{t.uiIntegration.title}</h2>
            <p>{t.uiIntegration.description}</p>
            
            <h3>{language === 'en' ? 'Composable Structure' : 'Composable構造'}</h3>
            <CodeBlock 
              code={t.uiIntegration.composableStructure}
              language="kotlin"
              title="LuminaMainScreen.kt"
            />
            
            <h3>{language === 'en' ? 'ViewModel Integration' : 'ViewModel統合'}</h3>
            <CodeBlock 
              code={t.uiIntegration.viewModelIntegration}
              language="kotlin"
              title="LuminaViewModel.kt"
            />
          </section>

          <section className="docs-section">
            <h2>{t.serviceIntegration.title}</h2>
            <p>{t.serviceIntegration.description}</p>
            
            <h3>{language === 'en' ? 'Service Implementation' : 'サービス実装'}</h3>
            <CodeBlock 
              code={t.serviceIntegration.serviceImplementation}
              language="kotlin"
              title="LuminaService.kt"
            />
            
            <h3>{language === 'en' ? 'Manifest Integration' : 'マニフェスト統合'}</h3>
            <CodeBlock 
              code={t.serviceIntegration.manifestIntegration}
              language="xml"
              title="AndroidManifest.xml"
            />
          </section>

          <section className="docs-section">
            <h2>{t.nativeIntegration.title}</h2>
            <p>{t.nativeIntegration.description}</p>
            
            <h3>{language === 'en' ? 'JNI Interface' : 'JNIインターフェース'}</h3>
            <CodeBlock 
              code={t.nativeIntegration.jniInterface}
              language="java"
              title="NativeBridge.java"
            />
            
            <h3>{language === 'en' ? 'C++ Implementation' : 'C++実装'}</h3>
            <CodeBlock 
              code={t.nativeIntegration.cppImplementation}
              language="cpp"
              title="jni_bridge.cpp"
            />
          </section>

          <section className="docs-section">
            <h2>{t.dataFlow.title}</h2>
            <p>{t.dataFlow.description}</p>
            
            <h3>{language === 'en' ? 'Data Flow Diagram' : 'データフロー図'}</h3>
            <CodeBlock 
              code={t.dataFlow.flowDiagram}
              language="text"
              title="Data Flow"
            />
            
            <h3>{language === 'en' ? 'Implementation Example' : '実装例'}</h3>
            <CodeBlock 
              code={t.dataFlow.implementation}
              language="kotlin"
              title="ServerConnectionFlow.kt"
            />
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsIntegrationGuidesPage;
