import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsGameModulesPage: React.FC = () => {
  const { language } = useLanguage();

  const content = {
    en: {
      title: 'Game Modules System',
      subtitle: 'Comprehensive guide to LuminaV4\'s modular game enhancement system',
      introduction: {
        title: 'Module System Overview',
        description: 'LuminaV4\'s game module system provides a powerful and flexible framework for enhancing Minecraft gameplay. Each module is designed as an independent component that can be enabled, disabled, and configured individually, allowing users to customize their experience precisely.',
        benefits: [
          'Modular architecture for easy maintenance and updates',
          'Real-time enable/disable functionality',
          'Comprehensive configuration system with value validation',
          'Category-based organization for better management',
          'Event-driven architecture for optimal performance',
          'Remote control support via RemoteLink system'
        ]
      },
      moduleTree: {
        title: 'Module Structure Tree',
        description: 'The game modules are organized in a hierarchical structure based on functionality and purpose.',
        tree: `📁 game/module/
├── 📁 api/                          # Core module framework
│   ├── 📄 Element.kt                # Base module class
│   ├── 📄 CheatCategory.kt          # Module categories
│   ├── 📄 Value.kt                  # Configuration values
│   └── 📄 ModuleManager.kt          # Module lifecycle management
├── 📁 impl/                         # Module implementations
│   ├── 📁 combat/                   # Combat-related modules
│   │   ├── ⚔️ KillauraElement.kt     # Automatic entity targeting
│   │   ├── ⚔️ ReachElement.kt        # Extended attack range
│   │   ├── ⚔️ VelocityElement.kt     # Knockback modification
│   │   ├── ⚔️ CritBotElement.kt      # Critical hit automation
│   │   ├── ⚔️ TriggerBotElement.kt   # Automatic attack triggering
│   │   ├── ⚔️ AntiCrystalElement.kt  # End crystal protection
│   │   ├── ⚔️ DamageBoostElement.kt  # Damage amplification
│   │   ├── ⚔️ HitboxElement.kt       # Hitbox manipulation
│   │   ├── ⚔️ InfiniteAuraElement.kt # Unlimited range aura
│   │   ├── ⚔️ MaceAuraElement.kt     # Mace weapon automation
│   │   ├── ⚔️ OpFightBotElement.kt   # Advanced combat bot
│   │   ├── ⚔️ QuickAttackElement.kt  # Rapid attack system
│   │   └── ⚔️ TPAuraElement.kt       # Teleport-based aura
│   ├── 📁 motion/                   # Movement modules
│   │   ├── 🏃 SpeedElement.kt        # Movement speed boost
│   │   ├── 🏃 FlyElement.kt          # Flight capabilities
│   │   ├── 🏃 BhopElement.kt         # Bunny hop movement
│   │   ├── 🏃 HighJumpElement.kt     # Enhanced jumping
│   │   ├── 🏃 LongJumpElement.kt     # Extended jump distance
│   │   ├── 🏃 AirJumpElement.kt      # Mid-air jumping
│   │   ├── 🏃 AntiAFKElement.kt      # AFK prevention
│   │   ├── 🏃 AntiAcFly.kt           # Anti-cheat bypass flight
│   │   ├── 🏃 FullStopElement.kt     # Instant movement stop
│   │   ├── 🏃 GlideElement.kt        # Gliding mechanics
│   │   ├── 🏃 JetPackElement.kt      # Jetpack simulation
│   │   ├── 🏃 JitterFlyElement.kt    # Jitter-based flight
│   │   ├── 🏃 MotionFlyElement.kt    # Motion-based flight
│   │   ├── 🏃 MotionVarElement.kt    # Motion variables
│   │   ├── 🏃 SpiderElement.kt       # Wall climbing
│   │   ├── 🏃 SprintElement.kt       # Enhanced sprinting
│   │   └── 🏃 StepElement.kt         # Step height modification
│   ├── 📁 visual/                   # Visual enhancement modules
│   │   ├── 👁️ FullBrightElement.kt   # Maximum brightness
│   │   ├── 👁️ NightVisionElement.kt  # Night vision effect
│   │   ├── 👁️ AntiBlindElement.kt    # Blindness prevention
│   │   ├── 👁️ FreeCameraElement.kt   # Free camera mode
│   │   ├── 👁️ NameTagElement.kt      # Name tag modifications
│   │   ├── 👁️ NoFireElement.kt       # Fire overlay removal
│   │   ├── 👁️ NoHurtCameraElement.kt # Hurt camera disable
│   │   ├── 👁️ TextSpoofElement.kt    # Text manipulation
│   │   └── 👁️ ZoomElement.kt         # Zoom functionality
│   ├── 📁 world/                    # World interaction modules
│   │   ├── 🌍 AutoNavigatorElement.kt # Automatic navigation
│   │   ├── 🌍 AutoWalkElement.kt      # Automatic walking
│   │   ├── 🌍 FollowBotElement.kt     # Player following
│   │   ├── 🌍 HasteElement.kt         # Mining speed boost
│   │   ├── 🌍 JesusElement.kt         # Water walking
│   │   ├── 🌍 MinimapElement.kt       # Minimap display
│   │   ├── 🌍 NoClipElement.kt        # Collision bypass
│   │   ├── 🌍 PhaseElement.kt         # Block phasing
│   │   ├── 🌍 StrafeElement.kt        # Strafe movement
│   │   ├── 🌍 WorldDebuggerElement.kt # World debugging
│   │   ├── 🌍 WorldSaveElement.kt     # World saving
│   │   └── 🌍 WorldSaveTesterElement.kt # Save testing
│   ├── 📁 misc/                     # Miscellaneous modules
│   │   ├── 🔧 AntiKickElement.kt      # Kick prevention
│   │   ├── 🔧 ArrayListElement.kt     # Module list display
│   │   ├── 🔧 CrasherElement.kt       # Server crashing
│   │   ├── 🔧 DesyncElement.kt        # Desynchronization
│   │   ├── 🔧 ESPElement.kt           # Entity highlighting
│   │   ├── 🔧 KeyStrokes.kt           # Keystroke display
│   │   ├── 🔧 PlayerTracerElement.kt  # Player tracking lines
│   │   ├── 🔧 PositionLoggerElement.kt # Position logging
│   │   ├── 🔧 ReplayElement.kt        # Replay recording
│   │   ├── 🔧 SessionInfoElement.kt   # Session information
│   │   ├── 🔧 SpeedoMeterElement.kt   # Speed measurement
│   │   ├── 🔧 TargetHud.kt            # Target HUD display
│   │   ├── 🔧 TimeShiftElement.kt     # Time manipulation
│   │   ├── 🔧 ToggleSound.kt          # Toggle sound effects
│   │   ├── 🔧 WaterMarkElement.kt     # Watermark display
│   │   └── 🔧 WeatherControllerElement.kt # Weather control
│   └── 📁 effect/                   # Effect modules
│       ├── ✨ BaseEffectElement.kt    # Base effect class
│       └── ✨ EffectList.kt           # Effect management
└── 📁 entity/                       # Entity system
    ├── 👤 Entity.kt                  # Base entity class
    ├── 👤 LocalPlayer.kt             # Local player entity
    ├── 👤 Player.kt                  # Player entity
    ├── 👤 EntityUnknown.kt           # Unknown entities
    └── 👤 MobList.kt                 # Mob definitions`
      },
      moduleCategories: {
        title: 'Module Categories',
        description: 'Modules are organized into distinct categories based on their primary function and use case.',
        categories: [
          {
            name: 'Combat',
            icon: '⚔️',
            description: 'Modules focused on combat enhancement and PvP optimization',
            count: 13,
            examples: ['KillAura', 'Reach', 'Velocity', 'CritBot'],
            purpose: 'Enhance combat effectiveness, automate attacks, and provide tactical advantages in PvP scenarios'
          },
          {
            name: 'Motion',
            icon: '🏃',
            description: 'Movement and locomotion enhancement modules',
            count: 15,
            examples: ['Speed', 'Fly', 'BunnyHop', 'HighJump'],
            purpose: 'Modify player movement mechanics, enable new movement types, and bypass movement restrictions'
          },
          {
            name: 'Visual',
            icon: '👁️',
            description: 'Visual enhancement and rendering modification modules',
            count: 9,
            examples: ['FullBright', 'NightVision', 'ESP', 'Zoom'],
            purpose: 'Improve visibility, modify rendering effects, and provide visual information overlays'
          },
          {
            name: 'World',
            icon: '🌍',
            description: 'World interaction and environment manipulation modules',
            count: 12,
            examples: ['AutoNavigator', 'Jesus', 'NoClip', 'Phase'],
            purpose: 'Interact with the world environment, automate world-based actions, and bypass world restrictions'
          },
          {
            name: 'Miscellaneous',
            icon: '🔧',
            description: 'Utility modules and specialized functionality',
            count: 14,
            examples: ['AntiKick', 'ArrayList', 'SessionInfo', 'TargetHUD'],
            purpose: 'Provide utility functions, information displays, and specialized tools for enhanced gameplay'
          },
          {
            name: 'Effect',
            icon: '✨',
            description: 'Effect management and visual effect modules',
            count: 2,
            examples: ['BaseEffect', 'EffectList'],
            purpose: 'Manage visual effects, particle systems, and effect-based enhancements'
          }
        ]
      }
    },
    ja: {
      title: 'ゲームモジュールシステム',
      subtitle: 'LuminaV4のモジュラーゲーム強化システムの包括的ガイド',
      introduction: {
        title: 'モジュールシステム概要',
        description: 'LuminaV4のゲームモジュールシステムは、Minecraftゲームプレイを強化するための強力で柔軟なフレームワークを提供します。各モジュールは独立したコンポーネントとして設計されており、個別に有効化、無効化、設定できるため、ユーザーは体験を正確にカスタマイズできます。',
        benefits: [
          '簡単なメンテナンスと更新のためのモジュラーアーキテクチャ',
          'リアルタイム有効/無効機能',
          '値検証を含む包括的な設定システム',
          'より良い管理のためのカテゴリベース組織',
          '最適なパフォーマンスのためのイベント駆動アーキテクチャ',
          'RemoteLinkシステム経由のリモートコントロールサポート'
        ]
      },
      moduleTree: {
        title: 'モジュール構造ツリー',
        description: 'ゲームモジュールは機能と目的に基づいて階層構造で整理されています。',
        tree: `📁 game/module/
├── 📁 api/                          # コアモジュールフレームワーク
│   ├── 📄 Element.kt                # ベースモジュールクラス
│   ├── 📄 CheatCategory.kt          # モジュールカテゴリ
│   ├── 📄 Value.kt                  # 設定値
│   └── 📄 ModuleManager.kt          # モジュールライフサイクル管理
├── 📁 impl/                         # モジュール実装
│   ├── 📁 combat/                   # 戦闘関連モジュール
│   │   ├── ⚔️ KillauraElement.kt     # 自動エンティティターゲティング
│   │   ├── ⚔️ ReachElement.kt        # 拡張攻撃範囲
│   │   ├── ⚔️ VelocityElement.kt     # ノックバック修正
│   │   ├── ⚔️ CritBotElement.kt      # クリティカルヒット自動化
│   │   ├── ⚔️ TriggerBotElement.kt   # 自動攻撃トリガー
│   │   ├── ⚔️ AntiCrystalElement.kt  # エンドクリスタル保護
│   │   ├── ⚔️ DamageBoostElement.kt  # ダメージ増幅
│   │   ├── ⚔️ HitboxElement.kt       # ヒットボックス操作
│   │   ├── ⚔️ InfiniteAuraElement.kt # 無制限範囲オーラ
│   │   ├── ⚔️ MaceAuraElement.kt     # メイス武器自動化
│   │   ├── ⚔️ OpFightBotElement.kt   # 高度戦闘ボット
│   │   ├── ⚔️ QuickAttackElement.kt  # 高速攻撃システム
│   │   └── ⚔️ TPAuraElement.kt       # テレポートベースオーラ
│   ├── 📁 motion/                   # 移動モジュール
│   │   ├── 🏃 SpeedElement.kt        # 移動速度ブースト
│   │   ├── 🏃 FlyElement.kt          # 飛行機能
│   │   ├── 🏃 BhopElement.kt         # バニーホップ移動
│   │   ├── 🏃 HighJumpElement.kt     # 強化ジャンプ
│   │   ├── 🏃 LongJumpElement.kt     # 拡張ジャンプ距離
│   │   ├── 🏃 AirJumpElement.kt      # 空中ジャンプ
│   │   ├── 🏃 AntiAFKElement.kt      # AFK防止
│   │   ├── 🏃 AntiAcFly.kt           # アンチチート回避飛行
│   │   ├── 🏃 FullStopElement.kt     # 瞬間移動停止
│   │   ├── 🏃 GlideElement.kt        # 滑空メカニクス
│   │   ├── 🏃 JetPackElement.kt      # ジェットパックシミュレーション
│   │   ├── 🏃 JitterFlyElement.kt    # ジッターベース飛行
│   │   ├── 🏃 MotionFlyElement.kt    # モーションベース飛行
│   │   ├── 🏃 MotionVarElement.kt    # モーション変数
│   │   ├── 🏃 SpiderElement.kt       # 壁登り
│   │   ├── 🏃 SprintElement.kt       # 強化スプリント
│   │   └── 🏃 StepElement.kt         # ステップ高さ修正
│   ├── 📁 visual/                   # 視覚強化モジュール
│   │   ├── 👁️ FullBrightElement.kt   # 最大明度
│   │   ├── 👁️ NightVisionElement.kt  # 暗視効果
│   │   ├── 👁️ AntiBlindElement.kt    # 失明防止
│   │   ├── 👁️ FreeCameraElement.kt   # フリーカメラモード
│   │   ├── 👁️ NameTagElement.kt      # ネームタグ修正
│   │   ├── 👁️ NoFireElement.kt       # 火災オーバーレイ除去
│   │   ├── 👁️ NoHurtCameraElement.kt # ハートカメラ無効化
│   │   ├── 👁️ TextSpoofElement.kt    # テキスト操作
│   │   └── 👁️ ZoomElement.kt         # ズーム機能
│   ├── 📁 world/                    # ワールド相互作用モジュール
│   │   ├── 🌍 AutoNavigatorElement.kt # 自動ナビゲーション
│   │   ├── 🌍 AutoWalkElement.kt      # 自動歩行
│   │   ├── 🌍 FollowBotElement.kt     # プレイヤー追従
│   │   ├── 🌍 HasteElement.kt         # 採掘速度ブースト
│   │   ├── 🌍 JesusElement.kt         # 水上歩行
│   │   ├── 🌍 MinimapElement.kt       # ミニマップ表示
│   │   ├── 🌍 NoClipElement.kt        # 衝突回避
│   │   ├── 🌍 PhaseElement.kt         # ブロック位相
│   │   ├── 🌍 StrafeElement.kt        # ストレイフ移動
│   │   ├── 🌍 WorldDebuggerElement.kt # ワールドデバッグ
│   │   ├── 🌍 WorldSaveElement.kt     # ワールド保存
│   │   └── 🌍 WorldSaveTesterElement.kt # 保存テスト
│   ├── 📁 misc/                     # その他モジュール
│   │   ├── 🔧 AntiKickElement.kt      # キック防止
│   │   ├── 🔧 ArrayListElement.kt     # モジュールリスト表示
│   │   ├── 🔧 CrasherElement.kt       # サーバークラッシュ
│   │   ├── 🔧 DesyncElement.kt        # 非同期化
│   │   ├── 🔧 ESPElement.kt           # エンティティハイライト
│   │   ├── 🔧 KeyStrokes.kt           # キーストローク表示
│   │   ├── 🔧 PlayerTracerElement.kt  # プレイヤー追跡線
│   │   ├── 🔧 PositionLoggerElement.kt # 位置ログ
│   │   ├── 🔧 ReplayElement.kt        # リプレイ録画
│   │   ├── 🔧 SessionInfoElement.kt   # セッション情報
│   │   ├── 🔧 SpeedoMeterElement.kt   # 速度測定
│   │   ├── 🔧 TargetHud.kt            # ターゲットHUD表示
│   │   ├── 🔧 TimeShiftElement.kt     # 時間操作
│   │   ├── 🔧 ToggleSound.kt          # トグル音効果
│   │   ├── 🔧 WaterMarkElement.kt     # ウォーターマーク表示
│   │   └── 🔧 WeatherControllerElement.kt # 天候制御
│   └── 📁 effect/                   # エフェクトモジュール
│       ├── ✨ BaseEffectElement.kt    # ベースエフェクトクラス
│       └── ✨ EffectList.kt           # エフェクト管理
└── 📁 entity/                       # エンティティシステム
    ├── 👤 Entity.kt                  # ベースエンティティクラス
    ├── 👤 LocalPlayer.kt             # ローカルプレイヤーエンティティ
    ├── 👤 Player.kt                  # プレイヤーエンティティ
    ├── 👤 EntityUnknown.kt           # 不明エンティティ
    └── 👤 MobList.kt                 # モブ定義`
      },
      moduleCategories: {
        title: 'モジュールカテゴリ',
        description: 'モジュールは主要機能と使用ケースに基づいて明確なカテゴリに整理されています。',
        categories: [
          {
            name: '戦闘',
            icon: '⚔️',
            description: '戦闘強化とPvP最適化に焦点を当てたモジュール',
            count: 13,
            examples: ['KillAura', 'Reach', 'Velocity', 'CritBot'],
            purpose: '戦闘効果を向上させ、攻撃を自動化し、PvPシナリオで戦術的優位性を提供'
          },
          {
            name: '移動',
            icon: '🏃',
            description: '移動と運動強化モジュール',
            count: 15,
            examples: ['Speed', 'Fly', 'BunnyHop', 'HighJump'],
            purpose: 'プレイヤー移動メカニクスを修正し、新しい移動タイプを有効にし、移動制限を回避'
          },
          {
            name: '視覚',
            icon: '👁️',
            description: '視覚強化とレンダリング修正モジュール',
            count: 9,
            examples: ['FullBright', 'NightVision', 'ESP', 'Zoom'],
            purpose: '視認性を向上させ、レンダリング効果を修正し、視覚情報オーバーレイを提供'
          },
          {
            name: 'ワールド',
            icon: '🌍',
            description: 'ワールド相互作用と環境操作モジュール',
            count: 12,
            examples: ['AutoNavigator', 'Jesus', 'NoClip', 'Phase'],
            purpose: 'ワールド環境と相互作用し、ワールドベースのアクションを自動化し、ワールド制限を回避'
          },
          {
            name: 'その他',
            icon: '🔧',
            description: 'ユーティリティモジュールと専門機能',
            count: 14,
            examples: ['AntiKick', 'ArrayList', 'SessionInfo', 'TargetHUD'],
            purpose: 'ユーティリティ機能、情報表示、強化されたゲームプレイのための専門ツールを提供'
          },
          {
            name: 'エフェクト',
            icon: '✨',
            description: 'エフェクト管理と視覚エフェクトモジュール',
            count: 2,
            examples: ['BaseEffect', 'EffectList'],
            purpose: '視覚効果、パーティクルシステム、エフェクトベース強化を管理'
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.introduction.title}</h2>
            <p>{t.introduction.description}</p>
            
            <h3>{language === 'en' ? 'System Benefits' : 'システムの利点'}</h3>
            <ul>
              {t.introduction.benefits.map((benefit, index) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </section>

          <section className="docs-section">
            <h2>{t.moduleTree.title}</h2>
            <p>{t.moduleTree.description}</p>
            
            <div className="docs-tree-container">
              <CodeBlock 
                code={t.moduleTree.tree}
                language="text"
                title="Module Directory Structure"
                showLineNumbers={false}
              />
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.moduleCategories.title}</h2>
            <p>{t.moduleCategories.description}</p>
            
            <div className="feature-grid">
              {t.moduleCategories.categories.map((category, index) => (
                <div key={index} className="feature-item">
                  <h3>
                    <span className="category-icon">{category.icon}</span>
                    {category.name}
                    <span className="category-count">({category.count})</span>
                  </h3>
                  <p>{category.description}</p>
                  <div className="category-examples">
                    <h5>{language === 'en' ? 'Examples:' : '例:'}</h5>
                    <div className="example-tags">
                      {category.examples.map((example, exIndex) => (
                        <span key={exIndex} className="example-tag">{example}</span>
                      ))}
                    </div>
                  </div>
                  <div className="category-purpose">
                    <h5>{language === 'en' ? 'Purpose:' : '目的:'}</h5>
                    <p>{category.purpose}</p>
                  </div>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Module Implementation Example' : 'モジュール実装例'}</h2>
            <p>{language === 'en' ? 'Here\'s how a typical module is implemented in the LuminaV4 system, using KillAura as an example:' : 'LuminaV4システムでの典型的なモジュール実装方法を、KillAuraを例として示します：'}</p>

            <CodeBlock
              code={`// KillauraElement.kt - Combat module example
class KillauraElement(iconResId: Int = AssetManager.getAsset("ic_sword_cross_black_24dp")) : Element(
    name = "KillAura",
    category = CheatCategory.Combat,
    iconResId,
    displayNameResId = AssetManager.getString("module_killaura_display_name")
) {
    // Configuration values with validation
    private val playerOnly by boolValue("Players", false)
    private var mobsOnly by boolValue("Mobs", true)
    private var tpAuraEnabled by boolValue("TP Aura", false)
    private var strafe by boolValue("Strafe", false)
    private var rangeValue by floatValue("Range", 3.7f, 2f..7f)
    private var attackInterval by intValue("Delay", 5, 1..20)
    private var cpsValue by intValue("CPS", 5, 1..20)

    // State variables
    private var lastAttackTime = 0L
    private var strafeAngle = 0.0f

    // Main packet interception logic
    override fun beforePacketBound(interceptablePacket: InterceptablePacket) {
        if (!isEnabled) return

        val packet = interceptablePacket.packet
        if (packet is PlayerAuthInputPacket) {
            val currentTime = System.currentTimeMillis()
            val minAttackDelay = 1000L / cpsValue

            if (packet.tick % attackInterval == 0L &&
                (currentTime - lastAttackTime) >= minAttackDelay) {

                val closestEntities = searchForClosestEntities()
                if (closestEntities.isEmpty()) return

                closestEntities.forEach { entity ->
                    // Teleport aura functionality
                    if (tpAuraEnabled) {
                        teleportTo(entity, distanceToKeep)
                    }

                    // Execute attack
                    session.localPlayer.attack(entity)

                    // Strafe around target
                    if (strafe) {
                        strafeAroundTarget(entity)
                    }

                    lastAttackTime = currentTime
                }
            }
        }
    }

    // Entity targeting logic
    private fun Entity.isTarget(): Boolean {
        return when (this) {
            is LocalPlayer -> false
            is Player -> {
                if (playerOnly || (playerOnly && mobsOnly)) {
                    !this.isBot()
                } else false
            }
            is EntityUnknown -> {
                if (mobsOnly || (playerOnly && mobsOnly)) {
                    isMob()
                } else false
            }
            else -> false
        }
    }

    // Entity search and filtering
    private fun searchForClosestEntities(): List<Entity> {
        return session.level.entityMap.values
            .filter { entity ->
                entity.distance(session.localPlayer) < rangeValue &&
                entity.isTarget()
            }
    }
}`}
              language="kotlin"
              title="KillauraElement.kt"
            />
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Module Lifecycle' : 'モジュールライフサイクル'}</h2>
            <p>{language === 'en' ? 'Understanding how modules are managed throughout their lifecycle:' : 'モジュールがライフサイクル全体でどのように管理されるかを理解する：'}</p>

            <div className="docs-callout info">
              <h4>{language === 'en' ? 'Module States' : 'モジュール状態'}</h4>
              <ul>
                <li><strong>{language === 'en' ? 'Initialization:' : '初期化:'}</strong> {language === 'en' ? 'Module is created and configuration values are set up' : 'モジュールが作成され、設定値がセットアップされる'}</li>
                <li><strong>{language === 'en' ? 'Registration:' : '登録:'}</strong> {language === 'en' ? 'Module is registered with the GameManager and becomes available' : 'モジュールがGameManagerに登録され、利用可能になる'}</li>
                <li><strong>{language === 'en' ? 'Enabled:' : '有効:'}</strong> {language === 'en' ? 'Module is active and processing packets/events' : 'モジュールがアクティブでパケット/イベントを処理している'}</li>
                <li><strong>{language === 'en' ? 'Disabled:' : '無効:'}</strong> {language === 'en' ? 'Module is inactive but remains in memory for quick re-enabling' : 'モジュールは非アクティブだが、迅速な再有効化のためメモリに残る'}</li>
                <li><strong>{language === 'en' ? 'Cleanup:' : 'クリーンアップ:'}</strong> {language === 'en' ? 'Module resources are released when no longer needed' : '不要になったときにモジュールリソースが解放される'}</li>
              </ul>
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Configuration System' : '設定システム'}</h2>
            <p>{language === 'en' ? 'Modules use a sophisticated configuration system with type-safe values and validation:' : 'モジュールは型安全な値と検証を備えた洗練された設定システムを使用します：'}</p>

            <CodeBlock
              code={`// Configuration value types and usage
class ExampleModule : Element("Example", CheatCategory.Misc) {
    // Boolean configuration
    private val enableFeature by boolValue("Enable Feature", true)

    // Float with range validation
    private val rangeValue by floatValue("Range", 3.0f, 1.0f..10.0f)

    // Integer with constraints
    private val delayMs by intValue("Delay (ms)", 100, 50..1000)

    // String configuration
    private val targetName by stringValue("Target Name", "Player")

    // Enum selection
    private val mode by enumValue("Mode", Mode.NORMAL)

    // Color configuration
    private val highlightColor by colorValue("Color", Color.RED)

    // List of values
    private val excludedPlayers by listValue("Excluded Players", mutableListOf<String>())

    enum class Mode {
        NORMAL, AGGRESSIVE, DEFENSIVE
    }

    // Values are automatically saved/loaded and can be modified via:
    // - In-game GUI
    // - RemoteLink commands
    // - Configuration files
    // - API calls
}`}
              language="kotlin"
              title="Configuration System"
            />
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Event System Integration' : 'イベントシステム統合'}</h2>
            <p>{language === 'en' ? 'Modules can listen to various game events for reactive functionality:' : 'モジュールは反応的機能のために様々なゲームイベントをリッスンできます：'}</p>

            <div className="feature-grid">
              <div className="feature-item">
                <h3>{language === 'en' ? 'Packet Events' : 'パケットイベント'}</h3>
                <p>{language === 'en' ? 'Intercept and modify network packets before they are processed' : 'ネットワークパケットが処理される前に傍受・修正'}</p>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'Entity Events' : 'エンティティイベント'}</h3>
                <p>{language === 'en' ? 'React to entity spawning, movement, and state changes' : 'エンティティのスポーン、移動、状態変化に反応'}</p>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'World Events' : 'ワールドイベント'}</h3>
                <p>{language === 'en' ? 'Respond to world changes, block updates, and chunk loading' : 'ワールド変更、ブロック更新、チャンク読み込みに応答'}</p>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'Player Events' : 'プレイヤーイベント'}</h3>
                <p>{language === 'en' ? 'Handle player actions, inventory changes, and status updates' : 'プレイヤーアクション、インベントリ変更、ステータス更新を処理'}</p>
              </div>
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Performance Considerations' : 'パフォーマンス考慮事項'}</h2>
            <div className="docs-callout warning">
              <h4>{language === 'en' ? 'Optimization Guidelines' : '最適化ガイドライン'}</h4>
              <ul>
                <li>{language === 'en' ? 'Use efficient algorithms for entity searching and filtering' : 'エンティティ検索とフィルタリングに効率的なアルゴリズムを使用'}</li>
                <li>{language === 'en' ? 'Implement proper cooldowns to prevent excessive packet sending' : '過度なパケット送信を防ぐために適切なクールダウンを実装'}</li>
                <li>{language === 'en' ? 'Cache frequently accessed data to reduce computation overhead' : '計算オーバーヘッドを削減するために頻繁にアクセスされるデータをキャッシュ'}</li>
                <li>{language === 'en' ? 'Use event-driven patterns instead of continuous polling' : '継続的なポーリングの代わりにイベント駆動パターンを使用'}</li>
                <li>{language === 'en' ? 'Properly clean up resources when modules are disabled' : 'モジュールが無効化されたときにリソースを適切にクリーンアップ'}</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsGameModulesPage;
