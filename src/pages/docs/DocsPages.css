/* Documentation Pages Styles */
.docs-page {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.docs-page-header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.docs-page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.docs-page-subtitle {
  font-size: 1.2rem;
  color: var(--light-text);
  margin: 0;
  line-height: 1.6;
}

.docs-page-content {
  line-height: 1.8;
}

.docs-section {
  margin-bottom: 3rem;
}

.docs-section h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--accent-color);
}

.docs-section h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 1rem;
  margin-top: 2rem;
}

.docs-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
}

.docs-section p {
  color: var(--text-color);
  margin-bottom: 1rem;
  line-height: 1.8;
}

.docs-section ul, .docs-section ol {
  color: var(--text-color);
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.docs-section li {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

.docs-intro {
  font-size: 1.1rem;
  color: var(--text-color);
  background-color: var(--surface-color);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-color);
  margin-bottom: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature-item {
  background-color: var(--surface-color);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}



.feature-item h3 {
  color: var(--accent-color);
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.feature-item p {
  margin: 0;
  color: var(--light-text);
  font-size: 0.95rem;
}

.module-list {
  margin-top: 1.5rem;
}

.module-item {
  background-color: var(--surface-color);
  padding: 1.25rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
  transition: var(--transition);
}

.module-item:hover {
  border-color: rgba(109, 142, 255, 0.3);
  background-color: rgba(109, 142, 255, 0.05);
}

.module-item h4 {
  color: var(--accent-color);
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.module-item p {
  margin: 0;
  color: var(--light-text);
  font-size: 0.95rem;
}

.section-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.section-card {
  background-color: var(--surface-color);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  text-decoration: none;
  color: inherit;
  transition: var(--transition);
  position: relative;
  display: block;
}



.section-card h3 {
  color: var(--white);
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.section-card p {
  margin: 0;
  color: var(--light-text);
  font-size: 0.95rem;
  line-height: 1.6;
}

.section-arrow {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  color: var(--accent-color);
  opacity: 0.7;
  transition: var(--transition);
}



.docs-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.docs-table th {
  background-color: var(--dark-surface);
  color: var(--white);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.docs-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.docs-table tr:last-child td {
  border-bottom: none;
}

.docs-table tr:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

.docs-callout {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--accent-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.docs-callout.warning {
  border-left-color: #ffa500;
  background-color: rgba(255, 165, 0, 0.05);
}

.docs-callout.info {
  border-left-color: #00bcd4;
  background-color: rgba(0, 188, 212, 0.05);
}

.docs-callout.success {
  border-left-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

.docs-callout h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: var(--white);
}

.docs-callout p {
  margin-bottom: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .docs-page-title {
    font-size: 2rem;
  }
  
  .docs-page-subtitle {
    font-size: 1.1rem;
  }
  
  .feature-grid,
  .section-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .feature-item,
  .module-item,
  .section-card {
    padding: 1rem;
  }
  
  .docs-section h2 {
    font-size: 1.5rem;
  }
  
  .docs-section h3 {
    font-size: 1.2rem;
  }
  
  .docs-table {
    font-size: 0.9rem;
  }
  
  .docs-table th,
  .docs-table td {
    padding: 0.75rem;
  }
}

/* Tree structure styles */
.docs-tree-container {
  margin: 1.5rem 0;
  background: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.docs-tree-container .code-block {
  margin: 0;
  border-radius: 0;
  border: none;
}

/* Category styles */
.category-icon {
  font-size: 1.2em;
  margin-right: 0.5rem;
}

.category-count {
  font-size: 0.8em;
  color: var(--text-secondary);
  font-weight: normal;
  margin-left: 0.5rem;
}

.category-examples {
  margin: 1rem 0;
}

.category-examples h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.example-tag {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.category-purpose {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.category-purpose h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.category-purpose p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Table styles */
.docs-table-container {
  overflow-x: auto;
  margin: 1.5rem 0;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.docs-table-container .docs-table {
  margin: 0;
  border: none;
  border-radius: 0;
}

/* Module list styles */
.module-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.module-item {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.module-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.module-item h4 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.module-item h5 {
  margin: 1rem 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.module-item ul {
  margin: 0;
  padding-left: 1.5rem;
}

.module-item li {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Documentation links */
.docs-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.docs-link:hover {
  color: var(--accent-color);
  text-decoration: underline;
}

/* Page transition animations */
.docs-page {
  animation: pageSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateX(20px);
  will-change: opacity, transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

@keyframes pageSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Feature grid animations */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.feature-item {
  animation: scaleIn 0.6s ease-out forwards;
  opacity: 0;
  transform: scale(0.95);
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }
.feature-item:nth-child(5) { animation-delay: 0.5s; }
.feature-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Callout animations */
.docs-callout {
  animation: slideInLeft 0.7s ease-out forwards;
  opacity: 0;
  transform: translateX(-30px);
}

.docs-callout.info { animation-delay: 0.2s; }
.docs-callout.warning { animation-delay: 0.3s; }
.docs-callout.success { animation-delay: 0.1s; }

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Code block animations */
.code-block {
  animation: fadeInScale 0.8s ease-out forwards;
  opacity: 0;
  transform: scale(0.98);
}

@keyframes fadeInScale {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Table animations */
.docs-table-container {
  animation: slideInUp 0.7s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tree container animations */
.docs-tree-container {
  animation: expandIn 0.8s ease-out forwards;
  opacity: 0;
  transform: scaleY(0.9);
  transform-origin: top;
}

@keyframes expandIn {
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* Mobile-specific animations */
@media (max-width: 768px) {
  .docs-page {
    animation: pageSlideInMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(15px);
  }

  @keyframes pageSlideInMobile {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-item {
    animation: fadeInMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
  }

  .feature-item:nth-child(1) { animation-delay: 0.05s; }
  .feature-item:nth-child(2) { animation-delay: 0.1s; }
  .feature-item:nth-child(3) { animation-delay: 0.15s; }
  .feature-item:nth-child(4) { animation-delay: 0.2s; }
  .feature-item:nth-child(5) { animation-delay: 0.25s; }
  .feature-item:nth-child(6) { animation-delay: 0.3s; }

  @keyframes fadeInMobile {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .docs-callout {
    animation: fadeInMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
  }

  .code-block,
  .docs-table-container,
  .docs-tree-container {
    animation: fadeInMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
  }
}

/* Hover animations for interactive elements */
.feature-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}



.docs-callout {
  transition: transform 0.2s ease;
}



/* Accessibility: Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .docs-page,
  .feature-item,
  .docs-callout,
  .code-block,
  .docs-table-container,
  .docs-tree-container {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .feature-item:hover,
  .docs-callout:hover {
    transform: none;
  }
}
