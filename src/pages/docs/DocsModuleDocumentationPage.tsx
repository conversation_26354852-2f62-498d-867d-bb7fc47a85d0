import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsModuleDocumentationPage: React.FC = () => {
  const { language } = useLanguage();
  const content = {
    en: {
      title: 'Module Documentation',
      subtitle: 'In-depth documentation of LuminaV4 modules and their interactions',
      protocolModule: {
        title: 'Protocol Module',
        description: 'The Protocol module handles all Minecraft Bedrock Edition protocol communication. It provides encoding/decoding capabilities for multiple protocol versions and manages connection states.',
        structure: `Protocol/
├── bedrock-codec/          # Protocol encoding and decoding
│   ├── src/main/java/
│   │   └── org/cloudburstmc/protocol/bedrock/
│   │       ├── codec/      # Version-specific codecs
│   │       ├── data/       # Protocol data structures
│   │       └── packet/     # Packet definitions
├── bedrock-connection/     # Connection management
│   ├── src/main/java/
│   │   └── org/cloudburstmc/protocol/bedrock/
│   │       ├── netty/      # Netty integration
│   │       └── util/       # Connection utilities
└── common/                 # Shared protocol utilities
    └── src/main/java/
        └── org/cloudburstmc/protocol/common/`,
        features: [
          'Multi-version protocol support (1.7.0 to 1.21.50+)',
          'Automatic protocol version detection',
          'Packet encoding and decoding',
          'Connection state management',
          'Compression and encryption support'
        ],
        example: `// Protocol version detection
BedrockCodec codec = Bedrock.getDefaultCodec();
int protocolVersion = codec.getProtocolVersion();

// Packet handling
BedrockPacket packet = new LoginPacket();
packet.setProtocolVersion(protocolVersion);
packet.setChainData(chainData);

// Encode packet
ByteBuf buffer = codec.tryEncode(packet);`
      },
      networkModule: {
        title: 'Network Module',
        description: 'The Network module provides low-level networking capabilities including RakNet transport, query protocols, and RCON support.',
        components: [
          {
            name: 'RakNet Transport',
            description: 'Implements the RakNet protocol used by Minecraft Bedrock for UDP communication',
            features: [
              'Reliable UDP communication',
              'Packet fragmentation and reassembly',
              'Connection management',
              'Bandwidth optimization'
            ]
          },
          {
            name: 'Query Codec',
            description: 'Handles server query protocol for retrieving server information',
            features: [
              'Basic server status queries',
              'Full server status with player list',
              'Timeout and retry handling'
            ]
          },
          {
            name: 'RCON Codec',
            description: 'Remote console protocol implementation for server administration',
            features: [
              'Secure authentication',
              'Command execution',
              'Response handling'
            ]
          }
        ]
      },
      lunarisModule: {
        title: 'Lunaris Module',
        description: 'Lunaris is the core utility library providing essential functionality and helper methods used throughout the application.',
        structure: `Lunaris/
├── src/main/java/
│   └── com/project/lumina/lunaris/
│       ├── util/           # Utility classes
│       ├── math/           # Mathematical operations
│       ├── crypto/         # Cryptographic utilities
│       ├── io/             # Input/output helpers
│       └── collections/    # Collection utilities`,
        utilities: [
          {
            category: 'Mathematical Operations',
            description: 'Vector math, collision detection, and geometric calculations',
            example: `// Vector operations
Vector3f position = new Vector3f(x, y, z);
Vector3f direction = position.normalize();
float distance = position.distance(target);

// Collision detection
BoundingBox playerBox = new BoundingBox(player.getPosition(), player.getSize());
boolean collision = playerBox.intersects(blockBox);`
          },
          {
            category: 'Cryptographic Utilities',
            description: 'Encryption, hashing, and security-related operations',
            example: `// JWT token handling
String token = JwtUtils.createToken(payload, secret);
Claims claims = JwtUtils.parseToken(token, secret);

// Encryption utilities
byte[] encrypted = CryptoUtils.encrypt(data, key);
byte[] decrypted = CryptoUtils.decrypt(encrypted, key);`
          },
          {
            category: 'I/O Operations',
            description: 'File handling, stream processing, and data serialization',
            example: `// Configuration management
Config config = ConfigManager.load("lumina.json");
config.set("feature.enabled", true);
ConfigManager.save(config);

// Data serialization
byte[] serialized = SerializationUtils.serialize(object);
Object deserialized = SerializationUtils.deserialize(serialized);`
          }
        ]
      },
      pixieModule: {
        title: 'Pixie Module',
        description: 'Pixie provides ImGui integration for Android, enabling native GUI rendering with high performance and flexibility.',
        features: [
          'Native ImGui rendering on Android',
          'OpenGL ES integration',
          'Touch input handling',
          'Custom widget support',
          'Theme and styling system'
        ],
        implementation: `// ImGui context initialization
ImGuiContext context = ImGui.createContext();
ImGuiIO io = ImGui.getIO();
io.setDisplaySize(width, height);

// Rendering loop
ImGui.newFrame();
if (ImGui.begin("Lumina Settings")) {
    ImGui.text("Client Configuration");
    ImGui.separator();
    
    boolean enabled = ImGui.checkbox("Enable Feature", featureEnabled);
    float value = ImGui.sliderFloat("Sensitivity", sensitivity, 0.1f, 2.0f);
    
    if (ImGui.button("Apply Settings")) {
        applyConfiguration();
    }
}
ImGui.end();
ImGui.render();`
      },
      animateduxModule: {
        title: 'AnimatedUX Module',
        description: 'AnimatedUX provides custom animation capabilities for creating smooth and engaging user interface transitions.',
        animationTypes: [
          {
            type: 'Transition Animations',
            description: 'Smooth transitions between UI states and screens',
            example: `// Fade transition
AnimationBuilder.create()
    .fadeIn(duration = 300)
    .withEasing(Easing.EASE_OUT)
    .onComplete { /* callback */ }
    .start(targetView)`
          },
          {
            type: 'Property Animations',
            description: 'Animate view properties like position, scale, and rotation',
            example: `// Scale animation
AnimationBuilder.create()
    .scale(from = 0.8f, to = 1.0f)
    .duration(250)
    .withInterpolator(BounceInterpolator())
    .start(view)`
          },
          {
            type: 'Complex Sequences',
            description: 'Chain multiple animations together for complex effects',
            example: `// Animation sequence
AnimationSequence.create()
    .then(slideIn(direction = Direction.LEFT))
    .then(fadeIn(duration = 200))
    .then(scale(to = 1.1f, duration = 100))
    .then(scale(to = 1.0f, duration = 100))
    .start()`
          }
        ]
      }
    },
    ja: {
      title: 'モジュール文書',
      subtitle: 'LuminaV4モジュールとその相互作用の詳細ドキュメント',
      protocolModule: {
        title: 'プロトコルモジュール',
        description: 'プロトコルモジュールは、すべてのMinecraft Bedrock Editionプロトコル通信を処理します。複数のプロトコルバージョンのエンコード/デコード機能を提供し、接続状態を管理します。',
        structure: `Protocol/
├── bedrock-codec/          # プロトコルエンコードとデコード
│   ├── src/main/java/
│   │   └── org/cloudburstmc/protocol/bedrock/
│   │       ├── codec/      # バージョン固有のコーデック
│   │       ├── data/       # プロトコルデータ構造
│   │       └── packet/     # パケット定義
├── bedrock-connection/     # 接続管理
│   ├── src/main/java/
│   │   └── org/cloudburstmc/protocol/bedrock/
│   │       ├── netty/      # Netty統合
│   │       └── util/       # 接続ユーティリティ
└── common/                 # 共有プロトコルユーティリティ
    └── src/main/java/
        └── org/cloudburstmc/protocol/common/`,
        features: [
          'マルチバージョンプロトコルサポート（1.7.0から1.21.50+）',
          '自動プロトコルバージョン検出',
          'パケットエンコードとデコード',
          '接続状態管理',
          '圧縮と暗号化サポート'
        ],
        example: `// プロトコルバージョン検出
BedrockCodec codec = Bedrock.getDefaultCodec();
int protocolVersion = codec.getProtocolVersion();

// パケット処理
BedrockPacket packet = new LoginPacket();
packet.setProtocolVersion(protocolVersion);
packet.setChainData(chainData);

// パケットエンコード
ByteBuf buffer = codec.tryEncode(packet);`
      },
      networkModule: {
        title: 'ネットワークモジュール',
        description: 'ネットワークモジュールは、RakNetトランスポート、クエリプロトコル、RCONサポートを含む低レベルネットワーキング機能を提供します。',
        components: [
          {
            name: 'RakNetトランスポート',
            description: 'UDP通信用にMinecraft Bedrockで使用されるRakNetプロトコルを実装',
            features: [
              '信頼性のあるUDP通信',
              'パケット断片化と再組み立て',
              '接続管理',
              '帯域幅最適化'
            ]
          },
          {
            name: 'クエリコーデック',
            description: 'サーバー情報取得のためのサーバークエリプロトコルを処理',
            features: [
              '基本サーバーステータスクエリ',
              'プレイヤーリスト付き完全サーバーステータス',
              'タイムアウトと再試行処理'
            ]
          },
          {
            name: 'RCONコーデック',
            description: 'サーバー管理用のリモートコンソールプロトコル実装',
            features: [
              'セキュア認証',
              'コマンド実行',
              'レスポンス処理'
            ]
          }
        ]
      },
      lunarisModule: {
        title: 'Lunarisモジュール',
        description: 'Lunarisは、アプリケーション全体で使用される必須機能とヘルパーメソッドを提供するコアユーティリティライブラリです。',
        structure: `Lunaris/
├── src/main/java/
│   └── com/project/lumina/lunaris/
│       ├── util/           # ユーティリティクラス
│       ├── math/           # 数学的操作
│       ├── crypto/         # 暗号化ユーティリティ
│       ├── io/             # 入出力ヘルパー
│       └── collections/    # コレクションユーティリティ`,
        utilities: [
          {
            category: '数学的操作',
            description: 'ベクトル数学、衝突検出、幾何学的計算',
            example: `// ベクトル操作
Vector3f position = new Vector3f(x, y, z);
Vector3f direction = position.normalize();
float distance = position.distance(target);

// 衝突検出
BoundingBox playerBox = new BoundingBox(player.getPosition(), player.getSize());
boolean collision = playerBox.intersects(blockBox);`
          },
          {
            category: '暗号化ユーティリティ',
            description: '暗号化、ハッシュ化、セキュリティ関連操作',
            example: `// JWTトークン処理
String token = JwtUtils.createToken(payload, secret);
Claims claims = JwtUtils.parseToken(token, secret);

// 暗号化ユーティリティ
byte[] encrypted = CryptoUtils.encrypt(data, key);
byte[] decrypted = CryptoUtils.decrypt(encrypted, key);`
          },
          {
            category: 'I/O操作',
            description: 'ファイル処理、ストリーム処理、データシリアライゼーション',
            example: `// 設定管理
Config config = ConfigManager.load("lumina.json");
config.set("feature.enabled", true);
ConfigManager.save(config);

// データシリアライゼーション
byte[] serialized = SerializationUtils.serialize(object);
Object deserialized = SerializationUtils.deserialize(serialized);`
          }
        ]
      },
      pixieModule: {
        title: 'Pixieモジュール',
        description: 'PixieはAndroid用のImGui統合を提供し、高性能で柔軟性のあるネイティブGUIレンダリングを可能にします。',
        features: [
          'Android上でのネイティブImGuiレンダリング',
          'OpenGL ES統合',
          'タッチ入力処理',
          'カスタムウィジェットサポート',
          'テーマとスタイリングシステム'
        ],
        implementation: `// ImGuiコンテキスト初期化
ImGuiContext context = ImGui.createContext();
ImGuiIO io = ImGui.getIO();
io.setDisplaySize(width, height);

// レンダリングループ
ImGui.newFrame();
if (ImGui.begin("Lumina Settings")) {
    ImGui.text("Client Configuration");
    ImGui.separator();
    
    boolean enabled = ImGui.checkbox("Enable Feature", featureEnabled);
    float value = ImGui.sliderFloat("Sensitivity", sensitivity, 0.1f, 2.0f);
    
    if (ImGui.button("Apply Settings")) {
        applyConfiguration();
    }
}
ImGui.end();
ImGui.render();`
      },
      animateduxModule: {
        title: 'AnimatedUXモジュール',
        description: 'AnimatedUXは、スムーズで魅力的なユーザーインターフェース遷移を作成するためのカスタムアニメーション機能を提供します。',
        animationTypes: [
          {
            type: '遷移アニメーション',
            description: 'UI状態と画面間のスムーズな遷移',
            example: `// フェード遷移
AnimationBuilder.create()
    .fadeIn(duration = 300)
    .withEasing(Easing.EASE_OUT)
    .onComplete { /* コールバック */ }
    .start(targetView)`
          },
          {
            type: 'プロパティアニメーション',
            description: '位置、スケール、回転などのビュープロパティをアニメート',
            example: `// スケールアニメーション
AnimationBuilder.create()
    .scale(from = 0.8f, to = 1.0f)
    .duration(250)
    .withInterpolator(BounceInterpolator())
    .start(view)`
          },
          {
            type: '複雑なシーケンス',
            description: '複雑な効果のために複数のアニメーションを連鎖',
            example: `// アニメーションシーケンス
AnimationSequence.create()
    .then(slideIn(direction = Direction.LEFT))
    .then(fadeIn(duration = 200))
    .then(scale(to = 1.1f, duration = 100))
    .then(scale(to = 1.0f, duration = 100))
    .start()`
          }
        ]
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.protocolModule.title}</h2>
            <p>{t.protocolModule.description}</p>
            
            <CodeBlock 
              code={t.protocolModule.structure}
              language="text"
              title="Protocol Module Structure"
            />
            
            <h3>{language === 'en' ? 'Key Features' : '主要機能'}</h3>
            <ul>
              {t.protocolModule.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            
            <h3>{language === 'en' ? 'Usage Example' : '使用例'}</h3>
            <CodeBlock 
              code={t.protocolModule.example}
              language="java"
              title="Protocol Usage"
            />
          </section>

          <section className="docs-section">
            <h2>{t.networkModule.title}</h2>
            <p>{t.networkModule.description}</p>
            
            <div className="module-list">
              {t.networkModule.components.map((component, index) => (
                <div key={index} className="module-item">
                  <h4>{component.name}</h4>
                  <p>{component.description}</p>
                  <ul>
                    {component.features.map((feature, featureIndex) => (
                      <li key={featureIndex}>{feature}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </section>

          <section className="docs-section">
            <h2>{t.lunarisModule.title}</h2>
            <p>{t.lunarisModule.description}</p>
            
            <CodeBlock 
              code={t.lunarisModule.structure}
              language="text"
              title="Lunaris Module Structure"
            />
            
            {t.lunarisModule.utilities.map((utility, index) => (
              <div key={index}>
                <h3>{utility.category}</h3>
                <p>{utility.description}</p>
                <CodeBlock 
                  code={utility.example}
                  language="java"
                  title={utility.category}
                />
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.pixieModule.title}</h2>
            <p>{t.pixieModule.description}</p>
            
            <h3>{language === 'en' ? 'Features' : '機能'}</h3>
            <ul>
              {t.pixieModule.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            
            <h3>{language === 'en' ? 'Implementation Example' : '実装例'}</h3>
            <CodeBlock 
              code={t.pixieModule.implementation}
              language="java"
              title="ImGui Integration"
            />
          </section>

          <section className="docs-section">
            <h2>{t.animateduxModule.title}</h2>
            <p>{t.animateduxModule.description}</p>

            {t.animateduxModule.animationTypes.map((animType, index) => (
              <div key={index}>
                <h3>{animType.type}</h3>
                <p>{animType.description}</p>
                <CodeBlock
                  code={animType.example}
                  language="kotlin"
                  title={animType.type}
                />
              </div>
            ))}
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsModuleDocumentationPage;
