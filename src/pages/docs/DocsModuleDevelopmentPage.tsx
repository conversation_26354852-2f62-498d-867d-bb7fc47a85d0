import React from 'react';
import DocsLayout from '../../components/docs/DocsLayout';
import CodeBlock from '../../components/docs/CodeBlock';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsPages.css';

const DocsModuleDevelopmentPage: React.FC = () => {
  const { language } = useLanguage();

  const content = {
    en: {
      title: 'Module Development Guide',
      subtitle: 'Learn how to create custom modules for LuminaV4 using CloudBurstMC patterns',
      introduction: {
        title: 'Introduction to Module Development',
        description: 'LuminaV4 follows a modular architecture inspired by CloudBurstMC, allowing developers to create custom modules that extend the client\'s functionality. This guide will teach you how to create, structure, and integrate your own modules into the LuminaV4 ecosystem.',
        benefits: [
          'Modular architecture for better code organization',
          'Reusable components across different projects',
          'Easy integration with existing LuminaV4 systems',
          'Support for both Java and Kotlin development',
          'Native C++ integration capabilities',
          'Gradle-based build system for dependency management'
        ]
      },
      moduleStructure: {
        title: 'Module Structure',
        description: 'A LuminaV4 module follows a standardized directory structure that ensures consistency and proper integration with the main application.',
        structure: `MyCustomModule/
├── build.gradle.kts          # Module build configuration
├── proguard-rules.pro        # ProGuard rules for release builds
├── consumer-rules.pro        # Consumer ProGuard rules
├── src/
│   ├── main/
│   │   ├── AndroidManifest.xml    # Android manifest (for Android modules)
│   │   ├── java/com/yourpackage/  # Java source files
│   │   ├── kotlin/             # Kotlin source files (optional)
│   │   ├── cpp/               # Native C++ code (optional)
│   │   │   ├── CMakeLists.txt # CMake build configuration
│   │   │   └── *.cpp          # C++ source files
│   │   └── res/               # Android resources (if needed)
│   ├── test/                  # Unit tests
│   └── androidTest/           # Android instrumentation tests
└── README.md                  # Module documentation`,
        explanation: 'This structure follows Android library conventions while supporting the specific needs of LuminaV4 modules, including native code integration and protocol handling.'
      },
      creatingModule: {
        title: 'Creating a New Module',
        description: 'Follow these steps to create a new module for LuminaV4.',
        steps: [
          {
            step: '1. Create Module Directory',
            description: 'Create a new directory in the LuminaV4 root for your module',
            code: `# Create module directory
mkdir MyCustomModule
cd MyCustomModule

# Create source directory structure
mkdir -p src/main/java/com/yourpackage/mycustommodule
mkdir -p src/main/kotlin
mkdir -p src/main/cpp
mkdir -p src/test/java
mkdir -p src/androidTest/java`
          },
          {
            step: '2. Configure build.gradle.kts',
            description: 'Set up the Gradle build configuration for your module',
            code: `plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    // Add other plugins as needed
}

android {
    namespace = "com.yourpackage.mycustommodule"
    compileSdk = 35

    defaultConfig {
        minSdk = 28
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
        
        // For native modules
        externalNativeBuild {
            cmake {
                cppFlags += ""
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
    
    // For native modules
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}

dependencies {
    // Core LuminaV4 dependencies
    api(project(":Lunaris"))
    api(project(":Protocol:bedrock-codec"))
    api(project(":Protocol:bedrock-connection"))
    
    // Android dependencies
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    
    // Testing dependencies
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}`
          },
          {
            step: '3. Update settings.gradle.kts',
            description: 'Add your module to the project settings',
            code: `// In the root settings.gradle.kts file, add your module:
include(":MyCustomModule")

// Or if it's in a subdirectory:
include(":Category:MyCustomModule")`
          }
        ]
      },
      protocolModule: {
        title: 'Creating a Protocol Module',
        description: 'Protocol modules handle Minecraft packet communication. Here\'s how to create one following CloudBurstMC patterns.',
        example: `// 1. Create a custom packet class
public class CustomGamePacket implements BedrockPacket {
    private String message;
    private int playerId;
    
    @Override
    public void serialize(ByteBuf buffer, BedrockCodecHelper helper) {
        helper.writeString(buffer, message);
        buffer.writeIntLE(playerId);
    }
    
    @Override
    public void deserialize(ByteBuf buffer, BedrockCodecHelper helper) {
        message = helper.readString(buffer);
        playerId = buffer.readIntLE();
    }
    
    @Override
    public BedrockPacketType getPacketType() {
        return BedrockPacketType.CUSTOM_GAME_PACKET;
    }
    
    // Getters and setters
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    public int getPlayerId() { return playerId; }
    public void setPlayerId(int playerId) { this.playerId = playerId; }
}

// 2. Create a packet handler
public class CustomPacketHandler implements BedrockPacketHandler {
    @Override
    public boolean handle(CustomGamePacket packet) {
        System.out.println("Received custom packet: " + packet.getMessage() 
                         + " from player " + packet.getPlayerId());
        return true; // Packet handled
    }
}

// 3. Register the packet in your codec
public class CustomBedrockCodec {
    public static void registerCustomPackets(BedrockCodec.Builder builder) {
        builder.registerPacket(CustomGamePacket::new, 
                             CustomGamePacket.class, 
                             CUSTOM_PACKET_ID);
    }
}`
      },
      nativeModule: {
        title: 'Creating a Native Module',
        description: 'Native modules allow you to integrate C++ code for performance-critical operations. Here\'s how to create one.',
        cppExample: `// src/main/cpp/custom_module.cpp
#include <jni.h>
#include <string>
#include <android/log.h>

#define LOG_TAG "CustomModule"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

extern "C" {
    JNIEXPORT jstring JNICALL
    Java_com_yourpackage_mycustommodule_CustomModule_processData(
        JNIEnv *env, jobject thiz, jstring input) {

        const char *inputStr = env->GetStringUTFChars(input, nullptr);

        // Perform your custom processing here
        std::string result = "Processed: " + std::string(inputStr);

        env->ReleaseStringUTFChars(input, inputStr);
        return env->NewStringUTF(result.c_str());
    }

    JNIEXPORT jintArray JNICALL
    Java_com_yourpackage_mycustommodule_CustomModule_performCalculation(
        JNIEnv *env, jobject thiz, jintArray inputArray) {

        jsize length = env->GetArrayLength(inputArray);
        jint *input = env->GetIntArrayElements(inputArray, nullptr);

        // Create result array
        jintArray result = env->NewIntArray(length);
        jint *output = env->GetIntArrayElements(result, nullptr);

        // Perform calculations
        for (int i = 0; i < length; i++) {
            output[i] = input[i] * 2; // Example: double each value
        }

        env->ReleaseIntArrayElements(inputArray, input, 0);
        env->ReleaseIntArrayElements(result, output, 0);

        return result;
    }
}`,
        javaExample: `// Java interface for the native module
public class CustomModule {
    static {
        System.loadLibrary("custommodule");
    }

    // Native method declarations
    public native String processData(String input);
    public native int[] performCalculation(int[] inputArray);

    // Wrapper methods with error handling
    public String safeProcessData(String input) {
        try {
            return processData(input);
        } catch (UnsatisfiedLinkError e) {
            Log.e("CustomModule", "Native library not loaded", e);
            return "Error: Native processing unavailable";
        }
    }

    public int[] safePerformCalculation(int[] input) {
        try {
            return performCalculation(input);
        } catch (UnsatisfiedLinkError e) {
            Log.e("CustomModule", "Native library not loaded", e);
            return new int[0];
        }
    }
}`,
        cmakeExample: `# src/main/cpp/CMakeLists.txt
cmake_minimum_required(VERSION 3.22.1)
project("custommodule")

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required libraries
find_library(log-lib log)
find_library(android-lib android)

# Add your source files
add_library(custommodule SHARED
    custom_module.cpp
    # Add more .cpp files as needed
)

# Link libraries
target_link_libraries(custommodule
    \${log-lib}
    \${android-lib}
)

# Compiler flags for optimization
target_compile_options(custommodule PRIVATE
    -Wall
    -Wextra
    -O3
    -ffast-math
)`
      },
      networkModule: {
        title: 'Creating a Network Module',
        description: 'Network modules handle low-level networking operations. Here\'s an example based on the RakNet transport pattern.',
        example: `// 1. Create a custom network handler
public class CustomNetworkHandler extends ChannelInboundHandlerAdapter {
    private final CustomNetworkListener listener;
    
    public CustomNetworkHandler(CustomNetworkListener listener) {
        this.listener = listener;
    }
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof ByteBuf) {
            ByteBuf buffer = (ByteBuf) msg;
            try {
                processCustomData(buffer);
            } finally {
                buffer.release();
            }
        }
        ctx.fireChannelRead(msg);
    }
    
    private void processCustomData(ByteBuf buffer) {
        // Process your custom network data
        int messageType = buffer.readByte();
        int dataLength = buffer.readIntLE();
        
        if (buffer.readableBytes() >= dataLength) {
            byte[] data = new byte[dataLength];
            buffer.readBytes(data);
            listener.onCustomDataReceived(messageType, data);
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }
}

// 2. Create the network listener interface
public interface CustomNetworkListener {
    void onCustomDataReceived(int messageType, byte[] data);
    void onConnectionEstablished();
    void onConnectionLost();
}

// 3. Create the main network module class
public class CustomNetworkModule {
    private final EventLoopGroup eventLoopGroup;
    private final CustomNetworkListener listener;
    private Channel channel;
    
    public CustomNetworkModule(CustomNetworkListener listener) {
        this.eventLoopGroup = new NioEventLoopGroup();
        this.listener = listener;
    }
    
    public void connect(String host, int port) {
        Bootstrap bootstrap = new Bootstrap()
            .group(eventLoopGroup)
            .channel(NioSocketChannel.class)
            .handler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel ch) {
                    ch.pipeline().addLast(new CustomNetworkHandler(listener));
                }
            });
            
        bootstrap.connect(host, port).addListener(future -> {
            if (future.isSuccess()) {
                channel = ((ChannelFuture) future).channel();
                listener.onConnectionEstablished();
            }
        });
    }
    
    public void sendData(int messageType, byte[] data) {
        if (channel != null && channel.isActive()) {
            ByteBuf buffer = channel.alloc().buffer();
            buffer.writeByte(messageType);
            buffer.writeIntLE(data.length);
            buffer.writeBytes(data);
            channel.writeAndFlush(buffer);
        }
    }
    
    public void disconnect() {
        if (channel != null) {
            channel.close();
        }
        eventLoopGroup.shutdownGracefully();
    }
}`
      }
    },
    ja: {
      title: 'モジュール開発ガイド',
      subtitle: 'CloudBurstMCパターンを使用してLuminaV4用カスタムモジュールを作成する方法を学ぶ',
      introduction: {
        title: 'モジュール開発入門',
        description: 'LuminaV4はCloudBurstMCにインスパイアされたモジュラーアーキテクチャに従い、開発者がクライアントの機能を拡張するカスタムモジュールを作成できます。このガイドでは、LuminaV4エコシステムで独自のモジュールを作成、構造化、統合する方法を教えます。',
        benefits: [
          'より良いコード組織のためのモジュラーアーキテクチャ',
          '異なるプロジェクト間での再利用可能なコンポーネント',
          '既存のLuminaV4システムとの簡単な統合',
          'JavaとKotlin開発の両方をサポート',
          'ネイティブC++統合機能',
          '依存関係管理のためのGradleベースビルドシステム'
        ]
      },
      moduleStructure: {
        title: 'モジュール構造',
        description: 'LuminaV4モジュールは、一貫性とメインアプリケーションとの適切な統合を保証する標準化されたディレクトリ構造に従います。',
        structure: `MyCustomModule/
├── build.gradle.kts          # モジュールビルド設定
├── proguard-rules.pro        # リリースビルド用ProGuardルール
├── consumer-rules.pro        # コンシューマーProGuardルール
├── src/
│   ├── main/
│   │   ├── AndroidManifest.xml    # Androidマニフェスト（Androidモジュール用）
│   │   ├── java/com/yourpackage/  # Javaソースファイル
│   │   ├── kotlin/             # Kotlinソースファイル（オプション）
│   │   ├── cpp/               # ネイティブC++コード（オプション）
│   │   │   ├── CMakeLists.txt # CMakeビルド設定
│   │   │   └── *.cpp          # C++ソースファイル
│   │   └── res/               # Androidリソース（必要な場合）
│   ├── test/                  # ユニットテスト
│   └── androidTest/           # Androidインストルメンテーションテスト
└── README.md                  # モジュール文書`,
        explanation: 'この構造は、ネイティブコード統合とプロトコル処理を含むLuminaV4モジュールの特定のニーズをサポートしながら、Androidライブラリの規約に従います。'
      },
      creatingModule: {
        title: '新しいモジュールの作成',
        description: 'LuminaV4用の新しいモジュールを作成するには、以下の手順に従ってください。',
        steps: [
          {
            step: '1. モジュールディレクトリの作成',
            description: 'モジュール用にLuminaV4ルートに新しいディレクトリを作成',
            code: `# モジュールディレクトリを作成
mkdir MyCustomModule
cd MyCustomModule

# ソースディレクトリ構造を作成
mkdir -p src/main/java/com/yourpackage/mycustommodule
mkdir -p src/main/kotlin
mkdir -p src/main/cpp
mkdir -p src/test/java
mkdir -p src/androidTest/java`
          },
          {
            step: '2. build.gradle.ktsの設定',
            description: 'モジュール用のGradleビルド設定をセットアップ',
            code: `plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    // 必要に応じて他のプラグインを追加
}

android {
    namespace = "com.yourpackage.mycustommodule"
    compileSdk = 35

    defaultConfig {
        minSdk = 28
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
        
        // ネイティブモジュール用
        externalNativeBuild {
            cmake {
                cppFlags += ""
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
    
    // ネイティブモジュール用
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}

dependencies {
    // コアLuminaV4依存関係
    api(project(":Lunaris"))
    api(project(":Protocol:bedrock-codec"))
    api(project(":Protocol:bedrock-connection"))
    
    // Android依存関係
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    
    // テスト依存関係
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}`
          },
          {
            step: '3. settings.gradle.ktsの更新',
            description: 'プロジェクト設定にモジュールを追加',
            code: `// ルートsettings.gradle.ktsファイルで、モジュールを追加:
include(":MyCustomModule")

// またはサブディレクトリにある場合:
include(":Category:MyCustomModule")`
          }
        ]
      },
      protocolModule: {
        title: 'プロトコルモジュールの作成',
        description: 'プロトコルモジュールはMinecraftパケット通信を処理します。CloudBurstMCパターンに従って作成する方法は以下の通りです。',
        example: `// 1. カスタムパケットクラスを作成
public class CustomGamePacket implements BedrockPacket {
    private String message;
    private int playerId;
    
    @Override
    public void serialize(ByteBuf buffer, BedrockCodecHelper helper) {
        helper.writeString(buffer, message);
        buffer.writeIntLE(playerId);
    }
    
    @Override
    public void deserialize(ByteBuf buffer, BedrockCodecHelper helper) {
        message = helper.readString(buffer);
        playerId = buffer.readIntLE();
    }
    
    @Override
    public BedrockPacketType getPacketType() {
        return BedrockPacketType.CUSTOM_GAME_PACKET;
    }
    
    // ゲッターとセッター
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    public int getPlayerId() { return playerId; }
    public void setPlayerId(int playerId) { this.playerId = playerId; }
}

// 2. パケットハンドラーを作成
public class CustomPacketHandler implements BedrockPacketHandler {
    @Override
    public boolean handle(CustomGamePacket packet) {
        System.out.println("カスタムパケットを受信: " + packet.getMessage() 
                         + " プレイヤー " + packet.getPlayerId() + "から");
        return true; // パケット処理済み
    }
}

// 3. コーデックにパケットを登録
public class CustomBedrockCodec {
    public static void registerCustomPackets(BedrockCodec.Builder builder) {
        builder.registerPacket(CustomGamePacket::new, 
                             CustomGamePacket.class, 
                             CUSTOM_PACKET_ID);
    }
}`
      },
      nativeModule: {
        title: 'ネイティブモジュールの作成',
        description: 'ネイティブモジュールは、パフォーマンス重要な操作のためにC++コードを統合することを可能にします。作成方法は以下の通りです。',
        cppExample: `// src/main/cpp/custom_module.cpp
#include <jni.h>
#include <string>
#include <android/log.h>

#define LOG_TAG "CustomModule"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

extern "C" {
    JNIEXPORT jstring JNICALL
    Java_com_yourpackage_mycustommodule_CustomModule_processData(
        JNIEnv *env, jobject thiz, jstring input) {

        const char *inputStr = env->GetStringUTFChars(input, nullptr);

        // ここでカスタム処理を実行
        std::string result = "処理済み: " + std::string(inputStr);

        env->ReleaseStringUTFChars(input, inputStr);
        return env->NewStringUTF(result.c_str());
    }

    JNIEXPORT jintArray JNICALL
    Java_com_yourpackage_mycustommodule_CustomModule_performCalculation(
        JNIEnv *env, jobject thiz, jintArray inputArray) {

        jsize length = env->GetArrayLength(inputArray);
        jint *input = env->GetIntArrayElements(inputArray, nullptr);

        // 結果配列を作成
        jintArray result = env->NewIntArray(length);
        jint *output = env->GetIntArrayElements(result, nullptr);

        // 計算を実行
        for (int i = 0; i < length; i++) {
            output[i] = input[i] * 2; // 例：各値を2倍
        }

        env->ReleaseIntArrayElements(inputArray, input, 0);
        env->ReleaseIntArrayElements(result, output, 0);

        return result;
    }
}`,
        javaExample: `// ネイティブモジュール用Javaインターフェース
public class CustomModule {
    static {
        System.loadLibrary("custommodule");
    }

    // ネイティブメソッド宣言
    public native String processData(String input);
    public native int[] performCalculation(int[] inputArray);

    // エラーハンドリング付きラッパーメソッド
    public String safeProcessData(String input) {
        try {
            return processData(input);
        } catch (UnsatisfiedLinkError e) {
            Log.e("CustomModule", "ネイティブライブラリが読み込まれていません", e);
            return "エラー: ネイティブ処理が利用できません";
        }
    }

    public int[] safePerformCalculation(int[] input) {
        try {
            return performCalculation(input);
        } catch (UnsatisfiedLinkError e) {
            Log.e("CustomModule", "ネイティブライブラリが読み込まれていません", e);
            return new int[0];
        }
    }
}`,
        cmakeExample: `# src/main/cpp/CMakeLists.txt
cmake_minimum_required(VERSION 3.22.1)
project("custommodule")

# C++標準を設定
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 必要なライブラリを検索
find_library(log-lib log)
find_library(android-lib android)

# ソースファイルを追加
add_library(custommodule SHARED
    custom_module.cpp
    # 必要に応じて他の.cppファイルを追加
)

# ライブラリをリンク
target_link_libraries(custommodule
    \${log-lib}
    \${android-lib}
)

# 最適化用コンパイラフラグ
target_compile_options(custommodule PRIVATE
    -Wall
    -Wextra
    -O3
    -ffast-math
)`
      },
      networkModule: {
        title: 'ネットワークモジュールの作成',
        description: 'ネットワークモジュールは低レベルネットワーキング操作を処理します。RakNetトランスポートパターンに基づく例は以下の通りです。',
        example: `// 1. カスタムネットワークハンドラーを作成
public class CustomNetworkHandler extends ChannelInboundHandlerAdapter {
    private final CustomNetworkListener listener;
    
    public CustomNetworkHandler(CustomNetworkListener listener) {
        this.listener = listener;
    }
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof ByteBuf) {
            ByteBuf buffer = (ByteBuf) msg;
            try {
                processCustomData(buffer);
            } finally {
                buffer.release();
            }
        }
        ctx.fireChannelRead(msg);
    }
    
    private void processCustomData(ByteBuf buffer) {
        // カスタムネットワークデータを処理
        int messageType = buffer.readByte();
        int dataLength = buffer.readIntLE();
        
        if (buffer.readableBytes() >= dataLength) {
            byte[] data = new byte[dataLength];
            buffer.readBytes(data);
            listener.onCustomDataReceived(messageType, data);
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }
}

// 2. ネットワークリスナーインターフェースを作成
public interface CustomNetworkListener {
    void onCustomDataReceived(int messageType, byte[] data);
    void onConnectionEstablished();
    void onConnectionLost();
}

// 3. メインネットワークモジュールクラスを作成
public class CustomNetworkModule {
    private final EventLoopGroup eventLoopGroup;
    private final CustomNetworkListener listener;
    private Channel channel;
    
    public CustomNetworkModule(CustomNetworkListener listener) {
        this.eventLoopGroup = new NioEventLoopGroup();
        this.listener = listener;
    }
    
    public void connect(String host, int port) {
        Bootstrap bootstrap = new Bootstrap()
            .group(eventLoopGroup)
            .channel(NioSocketChannel.class)
            .handler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel ch) {
                    ch.pipeline().addLast(new CustomNetworkHandler(listener));
                }
            });
            
        bootstrap.connect(host, port).addListener(future -> {
            if (future.isSuccess()) {
                channel = ((ChannelFuture) future).channel();
                listener.onConnectionEstablished();
            }
        });
    }
    
    public void sendData(int messageType, byte[] data) {
        if (channel != null && channel.isActive()) {
            ByteBuf buffer = channel.alloc().buffer();
            buffer.writeByte(messageType);
            buffer.writeIntLE(data.length);
            buffer.writeBytes(data);
            channel.writeAndFlush(buffer);
        }
    }
    
    public void disconnect() {
        if (channel != null) {
            channel.close();
        }
        eventLoopGroup.shutdownGracefully();
    }
}`
      }
    }
  };

  const t = content[language];

  return (
    <DocsLayout>
      <div className="docs-page">
        <header className="docs-page-header">
          <h1 className="docs-page-title">{t.title}</h1>
          <p className="docs-page-subtitle">{t.subtitle}</p>
        </header>

        <div className="docs-page-content">
          <section className="docs-section">
            <h2>{t.introduction.title}</h2>
            <p>{t.introduction.description}</p>
            
            <h3>{language === 'en' ? 'Benefits of Modular Development' : 'モジュラー開発の利点'}</h3>
            <ul>
              {t.introduction.benefits.map((benefit, index) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </section>

          <section className="docs-section">
            <h2>{t.moduleStructure.title}</h2>
            <p>{t.moduleStructure.description}</p>
            
            <CodeBlock 
              code={t.moduleStructure.structure}
              language="text"
              title="Module Directory Structure"
            />
            
            <p>{t.moduleStructure.explanation}</p>
          </section>

          <section className="docs-section">
            <h2>{t.creatingModule.title}</h2>
            <p>{t.creatingModule.description}</p>
            
            {t.creatingModule.steps.map((step, index) => (
              <div key={index}>
                <h3>{step.step}</h3>
                <p>{step.description}</p>
                <CodeBlock 
                  code={step.code}
                  language={index === 0 ? "bash" : "gradle"}
                  title={step.step}
                />
              </div>
            ))}
          </section>

          <section className="docs-section">
            <h2>{t.protocolModule.title}</h2>
            <p>{t.protocolModule.description}</p>
            
            <CodeBlock 
              code={t.protocolModule.example}
              language="java"
              title="Protocol Module Example"
            />
          </section>

          <section className="docs-section">
            <h2>{t.nativeModule.title}</h2>
            <p>{t.nativeModule.description}</p>

            <h3>{language === 'en' ? 'C++ Implementation' : 'C++実装'}</h3>
            <CodeBlock
              code={t.nativeModule.cppExample}
              language="cpp"
              title="custom_module.cpp"
            />

            <h3>{language === 'en' ? 'Java Interface' : 'Javaインターフェース'}</h3>
            <CodeBlock
              code={t.nativeModule.javaExample}
              language="java"
              title="CustomModule.java"
            />

            <h3>{language === 'en' ? 'CMake Configuration' : 'CMake設定'}</h3>
            <CodeBlock
              code={t.nativeModule.cmakeExample}
              language="cmake"
              title="CMakeLists.txt"
            />
          </section>

          <section className="docs-section">
            <h2>{t.networkModule.title}</h2>
            <p>{t.networkModule.description}</p>

            <CodeBlock
              code={t.networkModule.example}
              language="java"
              title="Network Module Example"
            />
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Best Practices' : 'ベストプラクティス'}</h2>
            <div className="docs-callout info">
              <h4>{language === 'en' ? 'Module Development Guidelines' : 'モジュール開発ガイドライン'}</h4>
              <ul>
                <li>{language === 'en' ? 'Follow consistent naming conventions across your module' : 'モジュール全体で一貫した命名規則に従う'}</li>
                <li>{language === 'en' ? 'Implement proper error handling and logging' : '適切なエラーハンドリングとログ記録を実装'}</li>
                <li>{language === 'en' ? 'Write comprehensive unit tests for your module' : 'モジュールの包括的なユニットテストを作成'}</li>
                <li>{language === 'en' ? 'Document your public APIs and interfaces' : 'パブリックAPIとインターフェースを文書化'}</li>
                <li>{language === 'en' ? 'Use dependency injection for better testability' : 'テスト可能性向上のために依存性注入を使用'}</li>
                <li>{language === 'en' ? 'Keep modules focused on a single responsibility' : 'モジュールを単一責任に集中させる'}</li>
              </ul>
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Testing Your Module' : 'モジュールのテスト'}</h2>
            <p>{language === 'en' ? 'Proper testing ensures your module works correctly and integrates well with LuminaV4.' : '適切なテストにより、モジュールが正しく動作し、LuminaV4と適切に統合されることを保証します。'}</p>

            <div className="feature-grid">
              <div className="feature-item">
                <h3>{language === 'en' ? 'Unit Testing' : 'ユニットテスト'}</h3>
                <p>{language === 'en' ? 'Test individual components and methods in isolation using JUnit and Mockito.' : 'JUnitとMockitoを使用して、個々のコンポーネントとメソッドを分離してテスト。'}</p>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'Integration Testing' : '統合テスト'}</h3>
                <p>{language === 'en' ? 'Test how your module interacts with other LuminaV4 components using Android instrumentation tests.' : 'Androidインストルメンテーションテストを使用して、モジュールが他のLuminaV4コンポーネントとどのように相互作用するかをテスト。'}</p>
              </div>
              <div className="feature-item">
                <h3>{language === 'en' ? 'Performance Testing' : 'パフォーマンステスト'}</h3>
                <p>{language === 'en' ? 'Ensure your module meets performance requirements, especially for native code components.' : 'モジュールがパフォーマンス要件を満たすことを確認、特にネイティブコードコンポーネントについて。'}</p>
              </div>
            </div>
          </section>

          <section className="docs-section">
            <h2>{language === 'en' ? 'Module Integration' : 'モジュール統合'}</h2>
            <p>{language === 'en' ? 'Once your module is complete, integrate it with the main LuminaV4 application:' : 'モジュールが完成したら、メインのLuminaV4アプリケーションと統合します：'}</p>

            <ol>
              <li>{language === 'en' ? 'Add your module to the main app\'s dependencies in build.gradle.kts' : 'build.gradle.ktsでメインアプリの依存関係にモジュールを追加'}</li>
              <li>{language === 'en' ? 'Initialize your module in the application context' : 'アプリケーションコンテキストでモジュールを初期化'}</li>
              <li>{language === 'en' ? 'Register any necessary services or components' : '必要なサービスやコンポーネントを登録'}</li>
              <li>{language === 'en' ? 'Test the integration thoroughly' : '統合を徹底的にテスト'}</li>
              <li>{language === 'en' ? 'Update documentation and examples' : 'ドキュメントと例を更新'}</li>
            </ol>
          </section>
        </div>
      </div>
    </DocsLayout>
  );
};

export default DocsModuleDevelopmentPage;
