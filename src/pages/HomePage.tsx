import React, { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

interface GitHubAsset {
  download_count: number;
}

interface GitHubRelease {
  assets: GitHubAsset[];
}

const HomePage: React.FC = () => {
  const { language, isTransitioning } = useLanguage();
  const [repoStats, setRepoStats] = useState({
    stars: 32,
    forks: 6,
    license: 'GPL-3.0',
    downloads: 0
  });
  const [loading, setLoading] = useState(true);

  const luminaImages = ['lumina.png', 'lumina2.jpg', 'lumina3.png'];

  const content = {
    en: {
      title: 'Project Lumina',
      subtitle: '',
      description: 'Project Lumina is a Multi OS Minecraft Bedrock Edition utility client designed to enhance combat skills and provide bypasses for minigame servers. With a focus on delivering smoother PvP gameplay, Project Lumina offers advanced features that give players a very fair',
      emphasis: '(i mean very fair)',
      advantage: 'advantage in game.',
      downloadButton: 'Download',
      githubButton: 'GitHub',
      patreonButton: 'Patreon',
      keyFeatures: 'Key Features',
      features: {
        combat: {
          title: 'Combat Enhancement',
          description: 'Advanced PvP features that improve your combat capabilities'
        },
        bypass: {
          title: 'Server Bypasses',
          description: 'Specialized tools for minigame servers and competitive play'
        },
        performance: {
          title: 'Performance Optimized',
          description: 'Smooth gameplay with minimal impact on system resources'
        }
      },
      repositoryInfo: 'Repository Info',
      repoCards: {
        license: {
          title: 'License',
          description: 'GPL-3.0 License - You may use, modify, and distribute Lumina, provided the source code is shared under the same license.'
        },
        version: {
          title: 'Version',
          description: 'Lumina Client Version 4 - A fork of MuCuteClient by SuMuCheng and Protohax by Haxor.'
        },
        credits: {
          title: 'Credits',
          description: 'Developed by Project Lumina team with support from HavensGrace Studios by @aiko and @aoi.'
        }
      },
      stats: {
        stars: 'Stars',
        forks: 'Forks',
        downloads: 'Downloads'
      }
    },
    ja: {
      title: 'Project Lumina',
      subtitle: '',
      description: 'Project Luminaは、戦闘スキルを向上させ、ミニゲームサーバーのバイパスを提供するように設計されたマルチOS Minecraft Bedrock Editionユーティリティクライアントです。よりスムーズなPvPゲームプレイの提供に焦点を当て、Project Luminaはプレイヤーに非常に公平な',
      emphasis: '（つまり非常に公平な）',
      advantage: 'ゲーム内でのアドバンテージを提供します。',
      downloadButton: 'ダウンロード',
      githubButton: 'GitHub',
      patreonButton: 'Patreon',
      keyFeatures: '主な機能',
      features: {
        combat: {
          title: '戦闘強化',
          description: '戦闘能力を向上させる高度なPvP機能'
        },
        bypass: {
          title: 'サーバーバイパス',
          description: 'ミニゲームサーバーと競技プレイ用の専門ツール'
        },
        performance: {
          title: 'パフォーマンス最適化',
          description: 'システムリソースへの影響を最小限に抑えたスムーズなゲームプレイ'
        }
      },
      repositoryInfo: 'リポジトリ情報',
      repoCards: {
        license: {
          title: 'ライセンス',
          description: 'GPL-3.0ライセンス - 同じライセンスの下でソースコードを共有することを条件に、Luminaを使用、変更、配布することができます。'
        },
        version: {
          title: 'バージョン',
          description: 'Luminaクライアントバージョン4 - SuMuChengによるMuCuteClientとHaxorによるProtohaxのフォーク。'
        },
        credits: {
          title: 'クレジット',
          description: '@aikoと@aoiによるHavensGrace Studiosのサポートを受けて、Project Luminaチームによって開発されました。'
        }
      },
      stats: {
        stars: 'スター',
        forks: 'フォーク',
        downloads: 'ダウンロード'
      }
    }
  };

  const t = content[language];

  useEffect(() => {
    const fetchRepoData = async () => {
      try {
        const repoResponse = await fetch('https://api.github.com/repos/TheProjectLumina/LuminaClient');
        let stars = 32;
        let forks = 6;
        let license = 'GPL-3.0';
        
        if (repoResponse.ok) {
          const repoData = await repoResponse.json();
          stars = repoData.stargazers_count;
          forks = repoData.forks_count;
          license = repoData.license?.spdx_id || 'GPL-3.0';
        }
        
        const releasesResponse = await fetch('https://api.github.com/repos/TheProjectLumina/LuminaClient/releases');
        let totalDownloads = 0;
        
        if (releasesResponse.ok) {
          const releasesData = await releasesResponse.json() as GitHubRelease[];
          
          totalDownloads = releasesData.reduce((total: number, release: GitHubRelease) => {
            const releaseDownloads = release.assets.reduce((sum: number, asset: GitHubAsset) => sum + asset.download_count, 0);
            return total + releaseDownloads;
          }, 0);
        }

        setRepoStats({
          stars,
          forks,
          license,
          downloads: totalDownloads
        });
      } catch (error) {
        console.error('Error fetching repo data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRepoData();
  }, []);

  return (
    <div className={`home ${isTransitioning ? 'language-changing' : ''}`}>
      <div className="container">
        <section className="hero language-content">
          <h1 className="hero-title">{t.title}</h1>
          <p className="hero-subtitle">{t.subtitle}</p>

          <div className="repo-stats">
            <div className="stat-item">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              <span>{loading ? '...' : `${repoStats.stars} ${t.stats.stars}`}</span>
            </div>
            <div className="stat-item">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 3C6 3 7 3 7 4C7 5 6 5 6 5H3C3 5 2 5 2 6V21C2 21 2 22 3 22H17C17 22 18 22 18 21C18 20 17 20 17 20H5V7H6C6 7 7 7 7 8C7 9 6 9 6 9H8C8 9 9 9 9 8C9 7 8 7 8 7C8 7 8 5 6 3Z" fill="currentColor"/>
                <path d="M13 5C13 5 12 5 12 6V10H10C10 10 9 10 9 11C9 12 10 12 10 12H12V14H10C10 14 9 14 9 15C9 16 10 16 10 16H12V18C12 18 12 19 13 19C14 19 14 18 14 18V16H16C16 16 17 16 17 15C17 14 16 14 16 14H14V12H16C16 12 17 12 17 11C17 10 16 10 16 10H14V6C14 6 14 5 13 5Z" fill="currentColor"/>
              </svg>
              <span>{loading ? '...' : `${repoStats.forks} ${t.stats.forks}`}</span>
            </div>
            <div className="stat-item">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.5 2H6C4.89 2 4 2.89 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V7.5L14.5 2ZM16 14V16H8V14H16ZM16 11V13H8V11H16ZM13 8V3.5L17.5 8H13Z" fill="currentColor"/>
              </svg>
              <span>{repoStats.license}</span>
            </div>
            <div className="stat-item">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 3V16M12 16L7 11M12 16L17 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3 20H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
              <span>{loading ? '...' : `${repoStats.downloads.toLocaleString()} ${t.stats.downloads}`}</span>
            </div>
          </div>
          
          <div className="description">
            <p>
              {t.description} <span className="emphasis">{t.emphasis}</span> {t.advantage}
            </p>
          </div>

          <div className="cta-buttons">
            <a
              href="https://github.com/TheProjectLumina/LuminaClient/releases/latest"
              className="button primary"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 3V16M12 16L7 11M12 16L17 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3 20H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
              <span>{t.downloadButton}</span>
            </a>
            <a href="https://github.com/TheProjectLumina/LuminaClient" className="button secondary">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.477 2 2 6.477 2 12C2 16.418 4.865 20.166 8.839 21.489C9.339 21.581 9.5 21.277 9.5 21.012C9.5 20.775 9.492 20.063 9.489 19.192C6.726 19.826 6.139 17.959 6.139 17.959C5.685 16.811 5.028 16.508 5.028 16.508C4.128 15.883 5.095 15.895 5.095 15.895C6.092 15.965 6.624 16.928 6.624 16.928C7.521 18.457 8.97 18.007 9.52 17.752C9.611 17.099 9.87 16.649 10.153 16.419C7.93 16.187 5.596 15.332 5.596 11.551C5.596 10.399 6.007 9.456 6.644 8.718C6.542 8.465 6.178 7.501 6.744 6.131C6.744 6.131 7.586 5.863 9.478 7.151C10.295 6.929 11.15 6.819 12.002 6.814C12.852 6.819 13.707 6.929 14.526 7.151C16.416 5.863 17.256 6.131 17.256 6.131C17.824 7.501 17.459 8.465 17.357 8.718C17.996 9.456 18.403 10.399 18.403 11.551C18.403 15.343 16.065 16.184 13.834 16.412C14.189 16.694 14.5 17.254 14.5 18.102C14.5 19.31 14.488 20.686 14.488 21.012C14.488 21.279 14.648 21.586 15.155 21.486C19.137 20.161 22 16.415 22 12C22 6.477 17.523 2 12 2Z" fill="currentColor"/>
              </svg>
              {t.githubButton}
            </a>
            <a href="https://www.patreon.com/luminaproxy" className="button secondary">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.386 2C19.83 2 22 4.518 22 8.175C22 11.832 19.83 14.35 15.386 14.35C10.942 14.35 8.772 11.832 8.772 8.175C8.772 4.518 10.942 2 15.386 2ZM2 2H6.614V22H2V2Z" fill="currentColor"/>
              </svg>
              {t.patreonButton}
            </a>
          </div>
        </section>

        <section className="image-showcase">
          <div className="simple-image-grid">
            {luminaImages.map((image, index) => (
              <img
                key={index}
                src={`/assets/lumina/${image}`}
                alt={`Lumina screenshot ${index + 1}`}
                className="showcase-image"
              />
            ))}
          </div>
        </section>

        <section className="features language-content">
          <h2>{t.keyFeatures}</h2>
          <div className="feature-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 16L4 8L5.4 6.6L12 13.2L18.6 6.6L20 8L12 16Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>{t.features.combat.title}</h3>
              <p>{t.features.combat.description}</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM6.5 17.5L14.01 14.01L17.5 6.5L9.99 9.99L6.5 17.5ZM12 10.9C12.61 10.9 13.1 11.39 13.1 12C13.1 12.61 12.61 13.1 12 13.1C11.39 13.1 10.9 12.61 10.9 12C10.9 11.39 11.39 10.9 12 10.9Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>{t.features.bypass.title}</h3>
              <p>{t.features.bypass.description}</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5C17 19.5 21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z" fill="currentColor"/>
                </svg>
              </div>
              <h3>{t.features.performance.title}</h3>
              <p>{t.features.performance.description}</p>
            </div>
          </div>
        </section>

        <section className="features language-content">
          <h2 className="section-title">{t.repositoryInfo}</h2>
          <div className="repo-cards">
            <div className="repo-card">
              <div className="repo-card-header">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13 9H18.5L13 3.5V9ZM6 2H14L20 8V20C20 20.5304 19.7893 21.0391 19.4142 21.4142C19.0391 21.7893 18.5304 22 18 22H6C4.89 22 4 21.1 4 20V4C4 2.89 4.89 2 6 2ZM15 18V16H6V18H15ZM18 14V12H6V14H18Z" fill="currentColor"/>
                </svg>
                <h3>{t.repoCards.license.title}</h3>
              </div>
              <p>{t.repoCards.license.description}</p>
            </div>

            <div className="repo-card">
              <div className="repo-card-header">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.58 20 4 16.42 4 12C4 7.58 7.58 4 12 4C16.42 4 20 7.58 20 12C20 16.42 16.42 20 12 20Z" fill="currentColor"/>
                  <path d="M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z" fill="currentColor"/>
                </svg>
                <h3>{t.repoCards.version.title}</h3>
              </div>
              <p>{t.repoCards.version.description}</p>
            </div>

            <div className="repo-card">
              <div className="repo-card-header">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C8.14 2 5 5.14 5 9C5 14.25 12 22 12 22C12 22 19 14.25 19 9C19 5.14 15.86 2 12 2ZM12 11.5C10.62 11.5 9.5 10.38 9.5 9C9.5 7.62 10.62 6.5 12 6.5C13.38 6.5 14.5 7.62 14.5 9C14.5 10.38 13.38 11.5 12 11.5Z" fill="currentColor"/>
                </svg>
                <h3>{t.repoCards.credits.title}</h3>
              </div>
              <p>{t.repoCards.credits.description}</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default HomePage; 
