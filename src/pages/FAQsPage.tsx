import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const FAQsPage: React.FC = () => {
  const { language, isTransitioning } = useLanguage();
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const content = {
    en: {
      title: 'Frequently Asked Questions',
      faqData: [
    {
      question: "What is Lumina?",
      answer: "Lumina is a mod for Minecraft Bedrock Edition that adds extra features to make gameplay more fun and customizable."
    },
    {
      question: "What platforms does Lumina support?",
      answer: "<PERSON><PERSON> works on all Minecraft Bedrock Edition platforms, including PC, iOS, Android, Nintendo Switch, PlayStation, Xbox, and more."
    },
    {
      question: "Is <PERSON><PERSON> free to use?",
      answer: "Yes, <PERSON><PERSON> is free to download and use on any supported device."
    },
    {
      question: "How do I install <PERSON><PERSON>?",
      answer: "Download Lumina from our website and follow the simple installation steps for your device, such as PC, Android, or iOS."
    },
    {
      question: "Can I use Lumina on multiplayer servers?",
      answer: "Yes, <PERSON><PERSON> works on most servers. However, some servers may not allow mods, so it's best to check the server rules first."
    },
    {
      question: "Will Lumina get me banned from servers?",
      answer: "On multiplayer servers, using mods might risk a ban—always ask the server admins before using it."
    },
    {
      question: "Does Lumina work with the latest Minecraft version?",
      answer: "We regularly update Lumina to stay compatible with the latest versions of Minecraft Bedrock Edition. Check our website for the most recent update."
    },
    {
      question: "What kind of features does Lumina have?",
      answer: "Lumina adds enhancements like improved visuals, movement helpers, combat tools, and more to enrich your Minecraft experience."
    },
    {
      question: "What do I do if Lumina isn't working?",
      answer: "Ensure you have the latest version, restart Minecraft, and test it in single-player. If issues continue, visit our support page."
    },
    {
      question: "Where can I get help with Lumina?",
      answer: "You can find help on our support page or by joining our Discord community to ask questions and get assistance."
    },
    {
      question: "Does Lumina support all architecture?",
      answer: "Yes, 32-Bit & 64-Bit"
    },
    {
      question: "Does it work on Local Worlds?",
      answer: "Not yet."
    }
      ]
    },
    ja: {
      title: 'よくある質問',
      faqData: [
        {
          question: "Luminaとは何ですか？",
          answer: "LuminaはMinecraft Bedrock Editionのモッドで、ゲームプレイをより楽しく、カスタマイズ可能にする追加機能を提供します。"
        },
        {
          question: "Luminaはどのプラットフォームをサポートしていますか？",
          answer: "LuminaはAndroidデバイスで動作します。ネイティブAndroidアプリケーションとして特別に設計されています。"
        },
        {
          question: "Luminaは無料で使用できますか？",
          answer: "はい、Luminaはサポートされているデバイスで無料でダウンロードして使用できます。"
        },
        {
          question: "Luminaをインストールするにはどうすればよいですか？",
          answer: "GitHubリリースページからLuminaをダウンロードし、Androidデバイスのインストール手順に従ってください。"
        },
        {
          question: "マルチプレイヤーサーバーでLuminaを使用できますか？",
          answer: "はい、Luminaはほとんどのサーバーで動作します。ただし、一部のサーバーではモッドが許可されていない場合があるため、最初にサーバールールを確認することをお勧めします。"
        },
        {
          question: "Luminaを使用するとサーバーからBANされますか？",
          answer: "マルチプレイヤーサーバーでは、モッドの使用によりBANのリスクがある場合があります。使用前に必ずサーバー管理者に確認してください。"
        },
        {
          question: "Luminaは最新のMinecraftバージョンで動作しますか？",
          answer: "私たちは定期的にLuminaを更新して、Minecraft Bedrock Editionの最新バージョンとの互換性を保っています。最新の更新については、ウェブサイトをご確認ください。"
        },
        {
          question: "Luminaにはどのような機能がありますか？",
          answer: "Luminaは、改善されたビジュアル、移動ヘルパー、戦闘ツールなど、Minecraftエクスペリエンスを豊かにする機能強化を追加します。"
        },
        {
          question: "Luminaが動作しない場合はどうすればよいですか？",
          answer: "最新バージョンがあることを確認し、Minecraftを再起動し、シングルプレイヤーでテストしてください。問題が続く場合は、サポートページをご覧ください。"
        },
        {
          question: "Luminaのヘルプはどこで得られますか？",
          answer: "サポートページでヘルプを見つけるか、Discordコミュニティに参加して質問やサポートを受けることができます。"
        },
        {
          question: "Luminaはすべてのアーキテクチャをサポートしていますか？",
          answer: "はい、32ビットと64ビット"
        },
        {
          question: "ローカルワールドで動作しますか？",
          answer: "まだです。"
        }
      ]
    }
  };

  const t = content[language];

  return (
    <div className={`faqs-page ${isTransitioning ? 'language-changing' : ''}`}>
      <div className="container">
        <section className="faqs-section language-content">
          <h1 className="page-title">{t.title}</h1>

          <div className="faqs-content">
            {t.faqData.map((faq, index) => (
              <div 
                className={`faq-item ${openFaq === index ? 'active' : ''}`} 
                key={index}
                onClick={() => toggleFaq(index)}
              >
                <div className="faq-question">
                  <h3>{faq.question}</h3>
                  <div className="faq-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
                <div className="faq-answer">
                  <p>{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default FAQsPage; 