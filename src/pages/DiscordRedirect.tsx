import React, { useEffect } from 'react';

const DiscordRedirect: React.FC = () => {
  useEffect(() => {
    // The Discord invite URL
    const discordUrl = 'https://discord.com/invite/6kz3dcndrN';
    
    // Add a small delay before redirecting
    const redirectTimer = setTimeout(() => {
      window.location.href = discordUrl;
    }, 1000); // 1 second delay
    
    // Cleanup timer if component unmounts
    return () => clearTimeout(redirectTimer);
  }, []);

  return (
    <div className="home">
      <div className="container">
        <div className="redirect-message">
          <div className="spinner"></div>
          <h2>Redirecting to Discord...</h2>
          <p>You will be redirected to the Project Lumina Discord server in a moment.</p>
        </div>
      </div>
    </div>
  );
};

export default DiscordRedirect; 