import React, { useState, useEffect } from 'react';

const CustomCursor: React.FC = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [visible, setVisible] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [clicking, setClicking] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(min-width: 1024px) and (pointer: fine)');
    setIsDesktop(mediaQuery.matches);

    const handleMediaChange = (e: MediaQueryListEvent) => {
      setIsDesktop(e.matches);
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleMediaChange);
    } else {
      mediaQuery.addListener(handleMediaChange);
    }

    if (mediaQuery.matches) {
      const onMouseMove = (e: MouseEvent) => {
        setPosition({ x: e.clientX, y: e.clientY });
      };

      const onMouseEnter = () => {
        setVisible(true);
      };

      const onMouseLeave = () => {
        setVisible(false);
      };
      
      const onMouseDown = () => {
        setClicking(true);
      };
      
      const onMouseUp = () => {
        setClicking(false);
      };

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseenter', onMouseEnter);
      document.addEventListener('mouseleave', onMouseLeave);
      document.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mouseup', onMouseUp);

      setTimeout(() => {
        setVisible(true);
      }, 500);

      return () => {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseenter', onMouseEnter);
        document.removeEventListener('mouseleave', onMouseLeave);
        document.removeEventListener('mousedown', onMouseDown);
        document.removeEventListener('mouseup', onMouseUp);

        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleMediaChange);
        } else {
          mediaQuery.removeListener(handleMediaChange);
        }
      };
    }
  }, []);

  if (!isDesktop) return null;

  return (
    <div 
      className="custom-cursor-container" 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 9999,
        opacity: visible ? 1 : 0,
        transition: 'opacity 0.3s ease',
      }}
    >
      <svg 
        width="50" 
        height="50" 
        viewBox="0 0 50 50" 
        style={{
          position: 'absolute',
          left: `${position.x}px`,
          top: `${position.y}px`,
          transform: `translate(-50%, -50%) scale(${clicking ? 0.8 : 1})`,
          transition: 'transform 0.15s ease',
          willChange: 'left, top, transform',
          filter: 'drop-shadow(0 0 5px rgba(0, 0, 0, 0.3))'
        }}
      >
        <circle 
          cx="25" 
          cy="25" 
          r="15" 
          fill="none" 
          stroke="white" 
          strokeWidth="1.5"
          strokeDasharray={clicking ? "3,3" : "0,0"}
          opacity="0.8"
        />
        
        <circle 
          cx="25" 
          cy="25" 
          r={clicking ? 3 : 5} 
          fill="white" 
          opacity="0.9"
        />

        <line 
          x1="25" 
          y1="5" 
          x2="25" 
          y2="10" 
          stroke="white" 
          strokeWidth="1.5"
          opacity={clicking ? "0.3" : "0.6"} 
        />
        <line 
          x1="25" 
          y1="40" 
          x2="25" 
          y2="45" 
          stroke="white" 
          strokeWidth="1.5" 
          opacity={clicking ? "0.3" : "0.6"}
        />
        <line 
          x1="5" 
          y1="25" 
          x2="10" 
          y2="25" 
          stroke="white" 
          strokeWidth="1.5" 
          opacity={clicking ? "0.3" : "0.6"}
        />
        <line 
          x1="40" 
          y1="25" 
          x2="45" 
          y2="25" 
          stroke="white" 
          strokeWidth="1.5" 
          opacity={clicking ? "0.3" : "0.6"}
        />
      </svg>
    </div>
  );
};

export default CustomCursor; 