/* Documentation Layout Styles */
.docs-layout {
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
}

.docs-header {
  background-color: var(--dark-surface);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.docs-header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.docs-header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
  display: none;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.docs-home-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
  font-weight: 500;
}

.docs-home-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--accent-color);
}

.docs-breadcrumb {
  display: flex;
  align-items: center;
  color: var(--light-text);
  font-size: 0.9rem;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  opacity: 0.5;
}

.breadcrumb-current {
  color: var(--accent-color);
  font-weight: 500;
}

.docs-header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.language-toggle:hover {
  border-color: var(--accent-color);
  background-color: rgba(109, 142, 255, 0.1);
}

.docs-container {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
}

.docs-sidebar {
  width: 280px;
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  position: sticky;
  top: 80px;
  height: calc(100vh - 80px);
  overflow-y: auto;
  transition: var(--transition);
}

.docs-sidebar-content {
  padding: 2rem 0;
}

.docs-sidebar-header {
  padding: 0 1.5rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.docs-sidebar-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--white);
  margin: 0;
}

.docs-navigation {
  display: flex;
  flex-direction: column;
}

.docs-nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  color: var(--light-text);
  text-decoration: none;
  transition: var(--transition);
  border-left: 3px solid transparent;
  font-weight: 500;
}

.docs-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
}

.docs-nav-item.active {
  background-color: rgba(109, 142, 255, 0.1);
  color: var(--accent-color);
  border-left-color: var(--accent-color);
}

.docs-main {
  flex: 1;
  padding: 2rem;
  overflow-x: hidden;
}

.docs-content {
  max-width: 900px;
  margin: 0 auto;
}

.docs-overlay {
  display: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar-toggle {
    display: block;
  }

  .docs-sidebar {
    position: fixed;
    top: 80px;
    left: -280px;
    z-index: 200;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .docs-sidebar.open {
    left: 0;
  }

  .docs-overlay {
    display: block;
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 150;
    animation: fadeInOverlay 0.3s ease-out forwards;
    opacity: 0;
  }

  @keyframes fadeInOverlay {
    to {
      opacity: 1;
    }
  }

  .docs-main {
    padding: 1rem;
  }

  .docs-breadcrumb {
    display: none;
  }
}

/* Scrollbar Styling */
.docs-sidebar::-webkit-scrollbar {
  width: 6px;
}

.docs-sidebar::-webkit-scrollbar-track {
  background: var(--dark-surface);
}

.docs-sidebar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.docs-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--light-text);
}

/* Page transition animations */
.docs-main {
  position: relative;
  overflow: hidden;
}

.docs-content {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
  will-change: opacity, transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for sections */
.docs-section {
  animation: fadeInSection 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(15px);
}

.docs-section:nth-child(1) { animation-delay: 0.1s; }
.docs-section:nth-child(2) { animation-delay: 0.2s; }
.docs-section:nth-child(3) { animation-delay: 0.3s; }
.docs-section:nth-child(4) { animation-delay: 0.4s; }
.docs-section:nth-child(5) { animation-delay: 0.5s; }
.docs-section:nth-child(6) { animation-delay: 0.6s; }
.docs-section:nth-child(7) { animation-delay: 0.7s; }
.docs-section:nth-child(8) { animation-delay: 0.8s; }
.docs-section:nth-child(9) { animation-delay: 0.9s; }
.docs-section:nth-child(10) { animation-delay: 1.0s; }

@keyframes fadeInSection {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header animation */
.docs-page-header {
  animation: slideInFromTop 0.7s ease-out forwards;
  opacity: 0;
  transform: translateY(-30px);
}

@keyframes slideInFromTop {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation item hover animations */
.docs-nav-item {
  position: relative;
  overflow: hidden;
}

.docs-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(109, 142, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.docs-nav-item:hover::before {
  left: 100%;
}

/* Mobile-specific animations */
@media (max-width: 768px) {
  .docs-content {
    animation: fadeInUpMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(15px);
  }

  @keyframes fadeInUpMobile {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .docs-section {
    animation: fadeInSectionMobile 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
  }

  .docs-section:nth-child(1) { animation-delay: 0.05s; }
  .docs-section:nth-child(2) { animation-delay: 0.1s; }
  .docs-section:nth-child(3) { animation-delay: 0.15s; }
  .docs-section:nth-child(4) { animation-delay: 0.2s; }
  .docs-section:nth-child(5) { animation-delay: 0.25s; }
  .docs-section:nth-child(6) { animation-delay: 0.3s; }
  .docs-section:nth-child(7) { animation-delay: 0.35s; }
  .docs-section:nth-child(8) { animation-delay: 0.4s; }
  .docs-section:nth-child(9) { animation-delay: 0.45s; }
  .docs-section:nth-child(10) { animation-delay: 0.5s; }

  @keyframes fadeInSectionMobile {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .docs-page-header {
    animation: slideInFromTopMobile 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(-20px);
  }

  @keyframes slideInFromTopMobile {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .docs-content,
  .docs-section,
  .docs-page-header {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .docs-nav-item::before {
    display: none;
  }
}
