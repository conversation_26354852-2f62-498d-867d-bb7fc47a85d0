import React, { useState } from 'react';
import './CodeBlock.css';

interface CodeBlockProps {
  code: string;
  language: string;
  title?: string;
  showLineNumbers?: boolean;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ 
  code, 
  language, 
  title, 
  showLineNumbers = true 
}) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const highlightSyntax = (code: string, lang: string): string => {
    // Simple syntax highlighting for common languages
    let highlighted = code;

    if (lang === 'kotlin' || lang === 'java') {
      // Keywords
      highlighted = highlighted.replace(
        /\b(class|interface|fun|val|var|if|else|when|for|while|return|import|package|private|public|protected|internal|override|abstract|open|final|companion|object|data|sealed|enum|annotation|suspend|inline|crossinline|noinline|reified|lateinit|lazy|delegate|by|in|out|where|is|as|this|super|null|true|false|try|catch|finally|throw|throws)\b/g,
        '<span class="keyword">$1</span>'
      );
      
      // Strings
      highlighted = highlighted.replace(
        /"([^"\\]|\\.)*"/g,
        '<span class="string">$&</span>'
      );
      
      // Comments
      highlighted = highlighted.replace(
        /\/\/.*$/gm,
        '<span class="comment">$&</span>'
      );
      highlighted = highlighted.replace(
        /\/\*[\s\S]*?\*\//g,
        '<span class="comment">$&</span>'
      );
      
      // Numbers
      highlighted = highlighted.replace(
        /\b\d+(\.\d+)?\b/g,
        '<span class="number">$&</span>'
      );
    } else if (lang === 'xml' || lang === 'html') {
      // XML/HTML tags
      highlighted = highlighted.replace(
        /<\/?[\w\s="/.':;#-/?]+>/g,
        '<span class="tag">$&</span>'
      );
      
      // Attributes
      highlighted = highlighted.replace(
        /(\w+)=/g,
        '<span class="attribute">$1</span>='
      );
      
      // Attribute values
      highlighted = highlighted.replace(
        /="([^"]*)"/g,
        '="<span class="string">$1</span>"'
      );
    } else if (lang === 'gradle' || lang === 'groovy') {
      // Gradle/Groovy keywords
      highlighted = highlighted.replace(
        /\b(plugins|dependencies|implementation|api|compileOnly|runtimeOnly|testImplementation|androidTestImplementation|apply|from|project|version|group|android|compileSdk|minSdk|targetSdk|buildTypes|release|debug|sourceSets|main|test)\b/g,
        '<span class="keyword">$1</span>'
      );
      
      // Strings
      highlighted = highlighted.replace(
        /"([^"\\]|\\.)*"|'([^'\\]|\\.)*'/g,
        '<span class="string">$&</span>'
      );
      
      // Comments
      highlighted = highlighted.replace(
        /\/\/.*$/gm,
        '<span class="comment">$&</span>'
      );
    }

    return highlighted;
  };

  const highlightedCode = highlightSyntax(code, language);
  const highlightedLines = highlightedCode.split('\n');

  return (
    <div className="code-block">
      {title && (
        <div className="code-block-header">
          <span className="code-block-title">{title}</span>
          <span className="code-block-language">{language}</span>
        </div>
      )}
      <div className="code-block-content">
        <button 
          className="copy-button"
          onClick={copyToClipboard}
          title="Copy code"
        >
          {copied ? (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          ) : (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none"/>
              <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" strokeWidth="2" fill="none"/>
            </svg>
          )}
        </button>
        <pre className="code-pre">
          <code className={`language-${language}`}>
            {showLineNumbers ? (
              <table className="code-table">
                <tbody>
                  {highlightedLines.map((line, index) => (
                    <tr key={index}>
                      <td className="line-number">{index + 1}</td>
                      <td 
                        className="line-content"
                        dangerouslySetInnerHTML={{ __html: line || '&nbsp;' }}
                      />
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div dangerouslySetInnerHTML={{ __html: highlightedCode }} />
            )}
          </code>
        </pre>
      </div>
    </div>
  );
};

export default CodeBlock;
