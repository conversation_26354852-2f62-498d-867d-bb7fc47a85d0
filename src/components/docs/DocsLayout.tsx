import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import './DocsLayout.css';

interface DocsLayoutProps {
  children: React.ReactNode;
}

interface NavigationItem {
  path: string;
  title: string;
  titleJa: string;
}

const navigationItems: NavigationItem[] = [
  { path: '/docs/overview', title: 'Overview', titleJa: '概要' },
  { path: '/docs/codebase-overview', title: 'Codebase Overview', titleJa: 'コードベース概要' },
  { path: '/docs/module-documentation', title: 'Module Documentation', titleJa: 'モジュール文書' },
  { path: '/docs/module-development', title: 'Module Development', titleJa: 'モジュール開発' },
  { path: '/docs/remotelink-system', title: 'RemoteLink System', titleJa: 'RemoteLinkシステム' },
  { path: '/docs/game-modules', title: 'Game Modules', titleJa: 'ゲームモジュール' },
  { path: '/docs/custom-servers', title: 'Custom Servers', titleJa: 'カスタムサーバー' },
  { path: '/docs/application-usage', title: 'Application Usage', titleJa: 'アプリケーション使用法' },
  { path: '/docs/integration-guides', title: 'Integration Guides', titleJa: '統合ガイド' },
  { path: '/docs/technical-topics', title: 'Technical Topics', titleJa: '技術的トピック' },
];

const DocsLayout: React.FC<DocsLayoutProps> = ({ children }) => {
  const { language, toggleLanguage } = useLanguage();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="docs-layout">
      <div className="docs-header">
        <div className="docs-header-content">
          <div className="docs-header-left">
            <button 
              className="sidebar-toggle"
              onClick={toggleSidebar}
              aria-label="Toggle sidebar"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
            <Link to="/" className="docs-home-link">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>{language === 'en' ? 'Home' : 'ホーム'}</span>
            </Link>
            <div className="docs-breadcrumb">
              <span className="breadcrumb-separator">/</span>
              <span className="breadcrumb-current">
                {language === 'en' ? 'Documentation' : 'ドキュメント'}
              </span>
            </div>
          </div>
          <div className="docs-header-right">
            <button 
              className="language-toggle"
              onClick={toggleLanguage}
              aria-label="Toggle language"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z" fill="currentColor"/>
              </svg>
              <span>{language === 'en' ? 'JP' : 'EN'}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="docs-container">
        <aside className={`docs-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="docs-sidebar-content">
            <div className="docs-sidebar-header">
              <h3>{language === 'en' ? 'LuminaV4 Documentation' : 'LuminaV4 ドキュメント'}</h3>
            </div>
            <nav className="docs-navigation">
              {navigationItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`docs-nav-item ${location.pathname === item.path ? 'active' : ''}`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <span>{language === 'en' ? item.title : item.titleJa}</span>
                  {location.pathname === item.path && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  )}
                </Link>
              ))}
            </nav>
          </div>
        </aside>

        <main className="docs-main">
          <div key={location.pathname} className="docs-content">
            {children}
          </div>
        </main>
      </div>

      {sidebarOpen && (
        <div 
          className="docs-overlay"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default DocsLayout;
