/* Code Block Styles */
.code-block {
  margin: 1.5rem 0;
  border-radius: var(--border-radius);
  background-color: #0d1117;
  border: 1px solid var(--border-color);
  overflow: hidden;
  position: relative;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #161b22;
  border-bottom: 1px solid var(--border-color);
}

.code-block-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
}

.code-block-language {
  font-size: 0.8rem;
  color: var(--light-text);
  background-color: rgba(109, 142, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.code-block-content {
  position: relative;
}

.copy-button {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  color: var(--light-text);
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-color);
}

.code-pre {
  margin: 0;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  background: transparent;
}

.code-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.line-number {
  width: 3rem;
  padding-right: 1rem;
  text-align: right;
  color: #6e7681;
  font-size: 0.85rem;
  user-select: none;
  vertical-align: top;
  border-right: 1px solid #21262d;
}

.line-content {
  padding-left: 1rem;
  color: #e6edf3;
  vertical-align: top;
  white-space: pre;
  word-wrap: break-word;
}

/* Syntax Highlighting */
.keyword {
  color: #ff7b72;
  font-weight: 500;
}

.string {
  color: #a5d6ff;
}

.comment {
  color: #8b949e;
  font-style: italic;
}

.number {
  color: #79c0ff;
}

.tag {
  color: #7ee787;
}

.attribute {
  color: #79c0ff;
}

.function {
  color: #d2a8ff;
}

.variable {
  color: #ffa657;
}

.operator {
  color: #ff7b72;
}

.punctuation {
  color: #e6edf3;
}

/* Scrollbar for code blocks */
.code-pre::-webkit-scrollbar {
  height: 8px;
}

.code-pre::-webkit-scrollbar-track {
  background: #161b22;
}

.code-pre::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 4px;
}

.code-pre::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .code-block-header {
    padding: 0.5rem 0.75rem;
  }
  
  .code-block-title {
    font-size: 0.8rem;
  }
  
  .code-block-language {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .copy-button {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.4rem;
  }
  
  .code-pre {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  
  .line-number {
    width: 2.5rem;
    padding-right: 0.75rem;
    font-size: 0.75rem;
  }
  
  .line-content {
    padding-left: 0.75rem;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .code-block {
    background-color: #0d1117;
  }
  
  .code-block-header {
    background-color: #161b22;
  }
}
