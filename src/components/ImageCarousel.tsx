import React, { useState, useEffect } from 'react';

interface ImageCarouselProps {
  images: string[];
  basePath?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  basePath = '/assets/lumina/',
  autoPlay = true,
  autoPlayInterval = 5000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const nextSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return;
    setIsTransitioning(true);
    setCurrentIndex(index);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTransitioning(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [currentIndex]);

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, images.length]);

  const getVisibleImages = () => {
    const visibleImages = [];
    const totalImages = images.length;

    for (let i = -1; i <= 1; i++) {
      const index = (currentIndex + i + totalImages) % totalImages;
      visibleImages.push({
        src: `${basePath}${images[index]}`,
        index,
        position: i,
        isCenter: i === 0
      });
    }

    return visibleImages;
  };

  return (
    <div className="image-carousel">
      <div className="carousel-container">
        <button 
          className="carousel-nav carousel-nav-prev" 
          onClick={prevSlide}
          disabled={isTransitioning}
          aria-label="Previous image"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>

        <div className="carousel-track">
          {getVisibleImages().map((image, idx) => (
            <div
              key={`${image.index}-${currentIndex}`}
              className={`carousel-slide ${image.isCenter ? 'active' : ''} ${
                image.position < 0 ? 'prev' : image.position > 0 ? 'next' : ''
              }`}
              style={{
                transform: `translateX(${image.position * 100}%)`,
                zIndex: image.isCenter ? 3 : 1
              }}
            >
              <img
                src={image.src}
                alt={`Lumina screenshot ${image.index + 1}`}
                className="carousel-image"
                loading="lazy"
              />
            </div>
          ))}
        </div>

        <button 
          className="carousel-nav carousel-nav-next" 
          onClick={nextSlide}
          disabled={isTransitioning}
          aria-label="Next image"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      <div className="carousel-indicators">
        {images.map((_, index) => (
          <button
            key={index}
            className={`carousel-indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
