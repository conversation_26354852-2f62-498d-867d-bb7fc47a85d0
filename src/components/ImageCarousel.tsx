import React, { useState, useEffect } from 'react';

interface ImageCarouselProps {
  images: string[];
  basePath?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  basePath = '/assets/lumina/',
  autoPlay = true,
  autoPlayInterval = 5000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return;
    setIsTransitioning(true);
    setCurrentIndex(index);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true);
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
        setTimeout(() => setIsTransitioning(false), 500);
      }
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, images.length, isTransitioning]);

  const getVisibleSlides = () => {
    const slides = [];
    const totalImages = images.length;

    for (let i = -1; i <= 1; i++) {
      const index = (currentIndex + i + totalImages) % totalImages;
      slides.push({
        image: images[index],
        index,
        position: i,
        isActive: i === 0
      });
    }

    return slides;
  };

  return (
    <div className="image-carousel">
      <div className="carousel-container">
        <div className="carousel-track">
          {getVisibleSlides().map((slide) => (
            <div
              key={`${slide.index}-${currentIndex}`}
              className={`carousel-slide ${slide.isActive ? 'active' : ''} ${
                slide.position < 0 ? 'prev' : slide.position > 0 ? 'next' : ''
              }`}
              style={{
                transform: `translateX(${slide.position * 100}%) scale(${slide.isActive ? 1 : 0.8})`,
                filter: slide.isActive ? 'blur(0px) brightness(1)' : 'blur(2px) brightness(0.6)',
                opacity: slide.isActive ? 1 : 0.7,
                zIndex: slide.isActive ? 3 : 1,
                transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <img
                src={`${basePath}${slide.image}`}
                alt={`Lumina screenshot ${slide.index + 1}`}
                className="carousel-image"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="carousel-indicators">
        {images.map((_, index) => (
          <button
            key={index}
            className={`carousel-indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
