import React, { useState, useEffect } from 'react';

interface ImageCarouselProps {
  images: string[];
  basePath?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  basePath = '/assets/lumina/',
  autoPlay = true,
  autoPlayInterval = 5000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return;
    setIsTransitioning(true);
    setCurrentIndex(index);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true);
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
        setTimeout(() => setIsTransitioning(false), 500);
      }
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, images.length, isTransitioning]);

  const createExtendedImages = () => {
    return [...images, ...images, ...images];
  };

  const extendedImages = createExtendedImages();
  const extendedCurrentIndex = currentIndex + images.length;

  return (
    <div className="image-carousel">
      <div className="carousel-container">
        <div className="carousel-track">
          <div
            className="carousel-slides-container"
            style={{
              transform: `translateX(-${extendedCurrentIndex * (100 / 3)}%)`,
              transition: isTransitioning ? 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)' : 'none',
              width: '300%',
              display: 'flex'
            }}
          >
            {extendedImages.map((image, index) => {
              const relativeIndex = index - extendedCurrentIndex;
              const isActive = relativeIndex === 0;
              const isPrev = relativeIndex === -1;
              const isNext = relativeIndex === 1;
              const isVisible = Math.abs(relativeIndex) <= 1;

              return (
                <div
                  key={index}
                  className={`carousel-slide ${isActive ? 'active' : ''} ${
                    isPrev ? 'prev' : isNext ? 'next' : ''
                  }`}
                  style={{
                    flex: '0 0 33.333%',
                    opacity: isVisible ? (isActive ? 1 : 0.7) : 0,
                    transform: `scale(${isActive ? 1 : 0.8})`,
                    filter: isActive ? 'blur(0px) brightness(1)' : 'blur(2px) brightness(0.6)',
                    zIndex: isActive ? 3 : 1,
                    transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                >
                  <img
                    src={`${basePath}${image}`}
                    alt={`Lumina screenshot ${(index % images.length) + 1}`}
                    className="carousel-image"
                    loading="lazy"
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className="carousel-indicators">
        {images.map((_, index) => (
          <button
            key={index}
            className={`carousel-indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
