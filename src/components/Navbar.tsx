import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const Navbar: React.FC = () => {
  const location = useLocation();
  const { language, toggleLanguage, isTransitioning } = useLanguage();

  const navContent = {
    en: {
      home: 'Home',
      faqs: 'FAQs',
      about: 'About'
    },
    ja: {
      home: 'ホーム',
      faqs: 'よくある質問',
      about: 'について'
    }
  };

  const t = navContent[language];

  return (
    <nav className="navbar">
      <div className="navbar-floating">
        <div className="navbar-content">
          <Link to="/" className={`nav-item ${location.pathname === '/' ? 'active' : ''}`}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z" fill="currentColor"/>
            </svg>
            <span>{t.home}</span>
          </Link>

          <Link to="/faqs" className={`nav-item ${location.pathname === '/faqs' ? 'active' : ''}`}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 19H11V17H13V19ZM15.07 11.25L14.17 12.17C13.45 12.9 13 13.5 13 15H11V14.5C11 13.4 11.45 12.4 12.17 11.67L13.41 10.41C13.78 10.05 14 9.55 14 9C14 7.9 13.1 7 12 7C10.9 7 10 7.9 10 9H8C8 6.79 9.79 5 12 5C14.21 5 16 6.79 16 9C16 9.88 15.64 10.68 15.07 11.25Z" fill="currentColor"/>
            </svg>
            <span>{t.faqs}</span>
          </Link>

          <Link to="/about" className={`nav-item ${location.pathname === '/about' ? 'active' : ''}`}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V11H13V17ZM13 9H11V7H13V9Z" fill="currentColor"/>
            </svg>
            <span>{t.about}</span>
          </Link>

          <button
            className={`nav-item language-btn ${isTransitioning ? 'transitioning' : ''}`}
            onClick={toggleLanguage}
            aria-label="Toggle language"
            disabled={isTransitioning}
            title="Toggle language"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z" fill="currentColor"/>
            </svg>
            <span>{isTransitioning ? '...' : (language === 'en' ? 'JP' : 'EN')}</span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar; 