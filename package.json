{"name": "project-lumina", "version": "0.1.0", "private": true, "dependencies": {"@netlify/functions": "^3.1.5", "@types/node": "^16.18.38", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}